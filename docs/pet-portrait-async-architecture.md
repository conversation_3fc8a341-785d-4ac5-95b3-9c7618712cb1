# Pet Portrait 异步架构设计方案

## 📋 项目概述

本文档描述了将 Pet Portrait 网站从同步模式改为异步模式的完整架构设计，支持多个 AI 提供商（ApiCore、Replicate、KIE AI 等）的 webhook 回调机制。

## 🎯 设计目标

1. **用户体验优化**：立即响应，不阻塞用户操作
2. **系统性能提升**：减少服务器资源占用，支持高并发
3. **可扩展性**：支持更多 AI 提供商和任务类型
4. **可靠性**：任务状态持久化，支持故障恢复
5. **全局一致性**：与现有用户系统、积分系统、订单系统无缝集成

## 🏗️ 整体架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端用户界面   │    │   Next.js API   │    │   PostgreSQL    │
│                │    │                │    │                │
│ • 任务提交      │◄──►│ • 任务管理      │◄──►│ • 任务状态      │
│ • 进度显示      │    │ • Webhook 处理  │    │ • 用户数据      │
│ • 结果展示      │    │ • 积分扣除      │    │ • 积分记录      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │  AI 提供商群组   │
                    │                │
                    │ • ApiCore      │
                    │ • Replicate    │
                    │ • KIE AI       │
                    │ • 其他...      │
                    └─────────────────┘
```

## 📊 数据库设计

### 1. 新增表：AI 任务表 (ai_tasks)

```sql
CREATE TABLE ai_tasks (
  id INTEGER PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
  uuid VARCHAR(255) NOT NULL UNIQUE,
  user_uuid VARCHAR(255) NOT NULL,
  
  -- 任务基本信息
  task_type VARCHAR(50) NOT NULL,           -- 'pet_portrait', 'image_generation', etc.
  provider VARCHAR(50) NOT NULL,            -- 'apicore', 'replicate', 'kie', etc.
  provider_task_id VARCHAR(255),            -- 提供商返回的任务ID
  
  -- 任务状态
  status VARCHAR(50) NOT NULL DEFAULT 'pending',  -- 'pending', 'processing', 'completed', 'failed', 'cancelled'
  progress DECIMAL(5,2) DEFAULT 0.00,       -- 进度百分比 0.00-100.00
  
  -- 输入参数
  input_params JSONB NOT NULL,              -- 输入参数 JSON
  
  -- 输出结果
  result_urls JSONB,                        -- 生成的图片URL数组
  result_metadata JSONB,                    -- 结果元数据
  
  -- 错误信息
  error_code VARCHAR(50),
  error_message TEXT,
  
  -- 积分相关
  credits_cost INTEGER DEFAULT 0,           -- 消耗的积分
  credits_transaction_id VARCHAR(255),      -- 积分交易ID
  
  -- 时间戳
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  started_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  
  -- 外键约束
  FOREIGN KEY (user_uuid) REFERENCES users(uuid),
  FOREIGN KEY (credits_transaction_id) REFERENCES credits(trans_no)
);

-- 索引
CREATE INDEX idx_ai_tasks_user_uuid ON ai_tasks(user_uuid);
CREATE INDEX idx_ai_tasks_status ON ai_tasks(status);
CREATE INDEX idx_ai_tasks_provider_task_id ON ai_tasks(provider, provider_task_id);
CREATE INDEX idx_ai_tasks_created_at ON ai_tasks(created_at);
```

### 2. 扩展现有表

#### 2.1 用户表 (users) - 无需修改
现有的用户表结构已经满足需求。

#### 2.2 积分表 (credits) - 新增交易类型
```sql
-- 在现有的 trans_type 基础上新增：
-- 'ai_task_cost' - AI任务消费
-- 'ai_task_refund' - AI任务退款（失败时）
```

## 🛣️ API 路由设计

### 1. 任务管理 API

```
POST   /api/ai/tasks                    # 创建AI任务
GET    /api/ai/tasks                    # 获取用户任务列表
GET    /api/ai/tasks/[uuid]             # 获取特定任务详情
DELETE /api/ai/tasks/[uuid]             # 取消任务
```

### 2. Webhook 接收 API

```
POST   /api/webhooks/ai/[provider]      # 接收AI提供商回调
```

### 3. 专用业务 API

```
POST   /api/pet-portrait/generate       # 宠物肖像生成（异步）
GET    /api/pet-portrait/tasks          # 获取宠物肖像任务
```

## 📱 前端页面结构

### 1. 主要页面

```
/[locale]/(default)/
├── pet-portrait/                       # 宠物肖像主页面
│   ├── page.tsx                       # 生成界面
│   ├── gallery/                       # 作品展示
│   └── my-creations/                  # 我的作品
├── (console)/
│   ├── my-tasks/                      # 我的AI任务
│   └── task-history/                  # 任务历史
```

### 2. 组件设计

```
components/
├── ai/
│   ├── TaskSubmissionForm.tsx         # 任务提交表单
│   ├── TaskProgressCard.tsx           # 任务进度卡片
│   ├── TaskResultGallery.tsx          # 结果展示画廊
│   └── TaskStatusBadge.tsx            # 状态徽章
├── pet-portrait/
│   ├── PetUploadForm.tsx              # 宠物图片上传
│   ├── StyleSelector.tsx              # 风格选择器
│   └── ProviderSelector.tsx           # AI提供商选择
```

## 🔄 业务流程设计

### 1. 任务提交流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant A as API
    participant D as 数据库
    participant P as AI提供商
    participant C as 积分系统

    U->>F: 提交宠物肖像任务
    F->>A: POST /api/pet-portrait/generate
    A->>C: 检查用户积分
    C-->>A: 积分充足
    A->>C: 预扣积分
    A->>D: 创建任务记录 (pending)
    A->>P: 提交任务到AI提供商
    P-->>A: 返回 provider_task_id
    A->>D: 更新 provider_task_id
    A-->>F: 返回任务UUID
    F-->>U: 显示"任务已提交"
```

### 2. Webhook 处理流程

```mermaid
sequenceDiagram
    participant P as AI提供商
    participant W as Webhook API
    participant D as 数据库
    participant N as 通知系统

    P->>W: POST /api/webhooks/ai/[provider]
    W->>W: 验证请求签名
    W->>D: 查找对应任务
    alt 任务成功
        W->>D: 更新状态为 completed
        W->>D: 保存结果URLs
        W->>N: 发送成功通知
    else 任务失败
        W->>D: 更新状态为 failed
        W->>D: 保存错误信息
        W->>C: 退还积分
        W->>N: 发送失败通知
    end
```

### 3. 前端轮询流程

```mermaid
sequenceDiagram
    participant F as 前端
    participant A as API
    participant D as 数据库

    loop 每10秒轮询
        F->>A: GET /api/ai/tasks/[uuid]
        A->>D: 查询任务状态
        D-->>A: 返回任务信息
        A-->>F: 返回状态和进度
        alt 任务完成
            F->>F: 显示结果
            F->>F: 停止轮询
        else 任务进行中
            F->>F: 更新进度条
        else 任务失败
            F->>F: 显示错误信息
            F->>F: 停止轮询
        end
    end
```

## 💰 积分系统集成

### 1. 积分消费策略

```typescript
// 不同任务类型的积分消费
const TASK_CREDITS_COST = {
  pet_portrait: {
    apicore: 10,
    replicate: 8,
    kie: 12,
  },
  image_generation: {
    apicore: 5,
    replicate: 4,
    kie: 6,
  }
};
```

### 2. 积分处理流程

1. **任务提交时**：预扣积分，创建 pending 状态的积分记录
2. **任务成功时**：确认积分消费，更新积分记录状态
3. **任务失败时**：退还积分，创建退款记录

## 🔐 安全设计

### 1. Webhook 安全

```typescript
// Webhook 签名验证
function verifyWebhookSignature(
  payload: string,
  signature: string,
  secret: string
): boolean {
  const expectedSignature = crypto
    .createHmac('sha256', secret)
    .update(payload)
    .digest('hex');
  return crypto.timingSafeEqual(
    Buffer.from(signature),
    Buffer.from(expectedSignature)
  );
}
```

### 2. 任务权限控制

- 用户只能查看和操作自己的任务
- 管理员可以查看所有任务
- API Key 权限控制

### 3. 速率限制

```typescript
// 任务提交频率限制
const RATE_LIMITS = {
  per_user_per_minute: 5,
  per_user_per_hour: 50,
  per_user_per_day: 200,
};
```

## 📈 监控和日志

### 1. 关键指标

- 任务成功率
- 平均处理时间
- 各提供商性能对比
- 用户活跃度
- 积分消费统计

### 2. 日志记录

```typescript
// 任务生命周期日志
interface TaskLog {
  task_uuid: string;
  event: 'created' | 'submitted' | 'processing' | 'completed' | 'failed';
  timestamp: Date;
  metadata: Record<string, any>;
}
```

## 🚀 部署和扩展

### 1. 环境变量配置

```bash
# AI 提供商配置
APICORE_API_KEY=xxx
KIE_API_KEY=xxx
REPLICATE_API_TOKEN=xxx

# Webhook 安全
WEBHOOK_SECRET_APICORE=xxx
WEBHOOK_SECRET_KIE=xxx
WEBHOOK_SECRET_REPLICATE=xxx

# 任务配置
MAX_CONCURRENT_TASKS_PER_USER=3
TASK_TIMEOUT_MINUTES=10
```

### 2. 扩展性考虑

- 支持新的 AI 提供商
- 支持新的任务类型
- 支持批量任务处理
- 支持任务优先级
- 支持任务调度

## 📝 实施计划

### Phase 1: 基础架构 (1-2周)
- [ ] 数据库表设计和迁移
- [ ] 基础 API 路由实现
- [ ] Webhook 接收框架

### Phase 2: 核心功能 (2-3周)
- [ ] 任务管理系统
- [ ] 积分系统集成
- [ ] 前端异步界面

### Phase 3: 优化和扩展 (1-2周)
- [ ] 性能优化
- [ ] 监控和日志
- [ ] 错误处理完善

### Phase 4: 测试和上线 (1周)
- [ ] 全面测试
- [ ] 生产环境部署
- [ ] 用户反馈收集

## 🔧 技术栈

- **后端**: Next.js API Routes, Drizzle ORM, PostgreSQL
- **前端**: React, TypeScript, Tailwind CSS
- **部署**: Cloudflare Workers, Cloudflare D1/Hyperdrive
- **存储**: Cloudflare R2
- **监控**: 自建日志系统

---

*本文档将随着项目进展持续更新*
