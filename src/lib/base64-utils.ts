/**
 * CF Workers兼容的base64工具函数
 * 使用Web标准API而不是Node.js Buffer
 */

/**
 * 将base64字符串转换为Uint8Array
 * 兼容CF Workers环境
 */
export function base64ToUint8Array(base64: string): Uint8Array {
  // 移除data URL前缀（如果有）
  const base64Clean = base64.replace(/^data:image\/[a-z]+;base64,/, '');
  
  try {
    // 使用atob解码base64（Web标准API）
    const binaryString = atob(base64Clean);
    const bytes = new Uint8Array(binaryString.length);
    
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    
    return bytes;
  } catch (error) {
    console.error('Failed to decode base64:', error);
    throw new Error('Invalid base64 string');
  }
}

/**
 * 将Uint8Array转换为base64字符串
 * 兼容CF Workers环境
 */
export function uint8ArrayToBase64(bytes: Uint8Array): string {
  let binaryString = '';
  
  for (let i = 0; i < bytes.length; i++) {
    binaryString += String.fromCharCode(bytes[i]);
  }
  
  return btoa(binaryString);
}

/**
 * 检查是否在CF Workers环境中运行
 */
export function isCloudflareWorkers(): boolean {
  // @ts-ignore
  return typeof globalThis.caches !== 'undefined' && 
         // @ts-ignore
         typeof globalThis.navigator?.userAgent === 'undefined' &&
         // @ts-ignore
         typeof globalThis.WorkerGlobalScope !== 'undefined';
}

/**
 * 安全的Buffer兼容函数
 * 在Node.js环境使用Buffer，在CF Workers使用Uint8Array
 */
export function safeBase64Decode(base64: string): Uint8Array {
  // 如果Buffer存在（Node.js环境），使用Buffer
  if (typeof Buffer !== 'undefined') {
    const base64Clean = base64.replace(/^data:image\/[a-z]+;base64,/, '');
    return new Uint8Array(Buffer.from(base64Clean, 'base64'));
  }
  
  // 否则使用Web标准API（CF Workers环境）
  return base64ToUint8Array(base64);
}

/**
 * 安全的Buffer兼容编码函数
 */
export function safeBase64Encode(bytes: Uint8Array): string {
  // 如果Buffer存在（Node.js环境），使用Buffer
  if (typeof Buffer !== 'undefined') {
    return Buffer.from(bytes).toString('base64');
  }
  
  // 否则使用Web标准API（CF Workers环境）
  return uint8ArrayToBase64(bytes);
}