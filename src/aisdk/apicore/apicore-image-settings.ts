export type ApicoreImageModelId = 
  | "gpt-4o-image"
  | "dall-e-3" 
  | "dall-e-2"
  | "midjourney-v6"
  | "stable-diffusion-xl"
  | "stable-diffusion-v1-5";

export interface ApicoreImageSettings {
  maxImagesPerCall?: number;
  quality?: "standard" | "hd";
  style?: "natural" | "vivid";
  size?: "256x256" | "512x512" | "1024x1024" | "1024x1792" | "1792x1024";
  // 图像编辑专用参数
  response_format?: "url" | "b64_json";
  // 支持的图像格式和大小限制
  image_format?: "png" | "jpeg" | "webp";
  max_image_size?: number; // 4MB limit according to docs
}