import type { FetchFunction } from "@ai-sdk/provider-utils";
import { loadSetting } from "@ai-sdk/provider-utils";
import { KieImageModel } from "./kie-image-model";
import { KieImageModelId, KieImageSettings } from "./kie-image-settings";

export interface KieProviderSettings {
  /**
   * KIE API key.
   */
  apiKey?: string;

  /**
   * Base URL for the KIE API.
   * @default "https://api.kie.ai"
   */
  baseURL?: string;

  /**
   * Additional headers to include in requests.
   */
  headers?: Record<string, string>;

  /**
   * Custom fetch function to use for requests.
   */
  fetch?: FetchFunction;
}

export interface KieProvider {
  /**
   * Create an image model.
   */
  image(
    modelId: KieImageModelId,
    settings?: KieImageSettings
  ): KieImageModel;
}

/**
 * Create a KIE provider instance.
 */
export function createKie(
  options: KieProviderSettings = {}
): KieProvider {
  const loadApiKey = () =>
    loadSetting({
      settingValue: options.apiKey,
      settingName: "apiKey",
      environmentVariableName: "KIE_API_KEY",
      description: "KIE API key",
    });

  return {
    image: (modelId: KieImageModelId, settings?: KieImageSettings) => {
      return new KieImageModel(modelId, settings ?? {}, {
        apiKey: loadApiKey(),
        provider: "kie",
        baseURL: options.baseURL ?? "https://api.kie.ai",
        headers: {
          ...options.headers,
        },
        fetch: options.fetch,
      });
    },
  };
}

/**
 * Default KIE provider instance.
 */
export const kie = createKie();
