import type { ImageModelV1, ImageModelV1CallWarning } from "@ai-sdk/provider";
import { KieImageModelId, KieImageSettings } from "./kie-image-settings";
import { FetchFunction } from "@ai-sdk/provider-utils";
import type { Resolvable } from "@ai-sdk/provider-utils";
import { newClient } from "./image-generation";


interface KieImageModelConfig {
  apiKey: string;
  provider: string;
  baseURL: string;
  headers: Resolvable<Record<string, string | undefined>>;
  fetch?: FetchFunction;
}

export class KieImageModel implements ImageModelV1 {
  readonly specificationVersion = "v1";
  readonly modelId: KieImageModelId;
  readonly settings: KieImageSettings;
  private readonly config: KieImageModelConfig;

  get provider(): string {
    return this.config.provider;
  }

  get maxImagesPerCall(): number {
    return this.settings.maxImagesPerCall ?? 4;
  }

  constructor(
    modelId: KieImageModelId,
    settings: KieImageSettings,
    config: KieImageModelConfig
  ) {
    this.modelId = modelId;
    this.settings = settings;
    this.config = config;
  }

  async doGenerate({
    prompt,
    n,
    aspectRatio,
    size,
    seed,
    providerOptions,
    headers,
    abortSignal,
  }: Parameters<ImageModelV1["doGenerate"]>[0]): Promise<
    Awaited<ReturnType<ImageModelV1["doGenerate"]>>
  > {
    const warnings: Array<ImageModelV1CallWarning> = [];
    let images: Array<Uint8Array> = [];
    const imgUrls: Array<string> = [];
    const currentDate = new Date();

    try {
      if (!this.config.apiKey) {
        throw new Error("KIE API key is not set");
      }

      const client = await newClient({
        apiKey: this.config.apiKey,
      });

      // 转换尺寸格式
      const kieSize = this.convertSize(size);
      console.log(`KIE size conversion: ${size} -> ${kieSize}`);

      // 原有的同步轮询模式
      console.log("使用 KIE 轮询同步模式");

      // 准备请求参数，注意要覆盖 providerOptions 中可能错误的 size
      const baseParams = {
        prompt,
        size: kieSize, // 使用转换后的正确格式
        nVariants: n || this.settings.nVariants || 1,
        isEnhance: this.settings.isEnhance || false,
        enableFallback: this.settings.enableFallback || false,
        fallbackModel: this.settings.fallbackModel || "FLUX_MAX",
        uploadCn: this.settings.uploadCn || false,
      };

      // 合并 providerOptions，但确保 size 使用我们转换后的格式
      const taskParams = {
        ...baseParams,
        ...(providerOptions.kie ?? {}),
        size: kieSize, // 强制使用转换后的格式
      };

      console.log("Creating KIE task with params:", taskParams);

      const task = await client.createTask(taskParams);

      if (!task.data || !task.data.taskId) {
        throw new Error(task.msg || "Failed to create KIE task");
      }

      console.log(`KIE task created: ${task.data.taskId}`);

      // 轮询任务状态
      const result = await this.pollTaskCompletion(
        client,
        task.data.taskId,
        abortSignal
      );

      if (result.data.successFlag === 2) {
        throw new Error(result.data.errorMessage || "KIE image generation failed");
      }

      console.log("KIE task completion result:", JSON.stringify(result.data, null, 2));

      if (!result.data.response?.resultUrls || result.data.response.resultUrls.length === 0) {
        console.error("KIE response structure:", {
          hasResponse: !!result.data.response,
          response: result.data.response,
          resultUrls: result.data.response?.resultUrls,
          resultUrlsLength: result.data.response?.resultUrls?.length
        });
        throw new Error("No images generated");
      }

      console.log("KIE generation completed, downloading images...");

      // 下载图片
      images = await this.downloadImages(result.data.response.resultUrls);
      imgUrls.push(...result.data.response.resultUrls);

      console.log(`Downloaded ${images.length} images from KIE`);

    } catch (error) {
      console.error("KIE image generation failed:", error);
      throw error;
    }

    return {
      images,
      warnings,
      response: {
        timestamp: currentDate,
        modelId: this.modelId,
        headers: undefined,
      },
    };
  }

  private convertSize(size?: string): "1:1" | "3:2" | "2:3" {
    if (!size) return this.settings.size || "1:1";
    
    // 如果已经是 KIE 格式，直接返回
    if (size === "1:1" || size === "3:2" || size === "2:3") {
      return size as "1:1" | "3:2" | "2:3";
    }
    
    // 转换标准格式 (如 "1024x1024") 到 KIE 格式
    const [width, height] = size.split('x').map(Number);
    if (isNaN(width) || isNaN(height)) {
      return this.settings.size || "1:1";
    }
    
    const ratio = width / height;
    
    if (Math.abs(ratio - 1) < 0.1) return "1:1";      // 正方形
    if (ratio > 1.3) return "3:2";                     // 横向
    if (ratio < 0.8) return "2:3";                     // 纵向
    return "1:1";                                       // 默认
  }

  private async pollTaskCompletion(
    client: any,
    taskId: string,
    abortSignal?: AbortSignal,
    maxWaitTime: number = 300000 // 5 minutes
  ): Promise<any> {
    const startTime = Date.now();
    const pollInterval = 30000; // 10 seconds

    while (Date.now() - startTime < maxWaitTime) {
      if (abortSignal?.aborted) {
        throw new Error("KIE task was aborted");
      }

      try {
        const status = await client.queryTask(taskId);

        console.log(`KIE task ${taskId} status: ${status.data.successFlag}, progress: ${status.data.progress}`);
        console.log("Full KIE status response:", JSON.stringify(status.data, null, 2));

        switch (status.data.successFlag) {
          case 1: // Success
            console.log("KIE task completed successfully");
            console.log("KIE success response:", JSON.stringify(status.data.response, null, 2));
            return status;

          case 0: // Generating
            console.log(`KIE task still generating, progress: ${(parseFloat(status.data.progress) * 100).toFixed(1)}%`);
            break;

          case 2: // Failed
            console.error("KIE task failed:", status.data.errorMessage);
            throw new Error(status.data.errorMessage || "KIE generation failed");

          default:
            console.log(`Unknown KIE task status: ${status.data.successFlag}`);
            console.log("Unknown status data:", JSON.stringify(status.data, null, 2));
            break;
        }

        // Wait before next poll
        await new Promise(resolve => setTimeout(resolve, pollInterval));
        
      } catch (error) {
        console.error("Error polling KIE task status:", error);
        throw error;
      }
    }

    throw new Error("KIE task timeout");
  }

  private async downloadImages(urls: string[]): Promise<Uint8Array[]> {
    const images: Uint8Array[] = [];

    for (const url of urls) {
      try {
        console.log(`Downloading image from: ${url}`);
        
        const response = await fetch(url);
        if (!response.ok) {
          throw new Error(`Failed to download image: ${response.status} ${response.statusText}`);
        }

        const arrayBuffer = await response.arrayBuffer();
        images.push(new Uint8Array(arrayBuffer));
        
        console.log(`Successfully downloaded image, size: ${arrayBuffer.byteLength} bytes`);
      } catch (error) {
        console.error(`Failed to download image from ${url}:`, error);
        throw error;
      }
    }

    return images;
  }
}
