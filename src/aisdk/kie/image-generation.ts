const baseUrl = "https://api.kie.ai/api/v1/gpt4o-image";

export interface KieTaskResponse {
  code: number;
  msg: string;
  data: {
    taskId: string;
  };
}

export interface KieTaskStatus {
  code: number;
  msg: string;
  data: {
    taskId: string;
    paramJson: string;
    completeTime: string | null;
    response: {
      result_urls: string[];
    } | null;
    successFlag: number; // 0: generating, 1: success, 2: failed
    status: string;
    errorCode: number | null;
    errorMessage: string | null;
    createTime: string;
    progress: string;
  };
}

export interface KieDownloadResponse {
  code: number;
  msg: string;
  data: {
    downloadUrl: string;
  };
}

export class KieImageGeneration {
  constructor(private apiKey: string) {}

  async createTask({
    prompt,
    filesUrl,
    maskUrl,
    size = "1:1",
    nVariants = 1,
    isEnhance = false,
    enableFallback = false,
    fallbackModel = "FLUX_MAX",
    uploadCn = false,
    callBackUrl,
    ...params
  }: {
    prompt: string;
    filesUrl?: string[];
    maskUrl?: string;
    size?: "1:1" | "3:2" | "2:3";
    nVariants?: number;
    isEnhance?: boolean;
    enableFallback?: boolean;
    fallbackModel?: "FLUX_MAX" | "GPT_IMAGE_1";
    uploadCn?: boolean;
    callBackUrl?: string;
    [key: string]: any;
  }): Promise<KieTaskResponse> {
    try {
      const uri = `${baseUrl}/generate`;
      const requestBody = {
        prompt,
        filesUrl,
        maskUrl,
        size,
        nVariants,
        isEnhance,
        enableFallback,
        fallbackModel,
        uploadCn,
        callBackUrl,
        ...params,
      };

      console.log("KIE Image Generation request:", uri, requestBody);

      const response = await fetch(uri, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("KIE API Error:", response.status, errorText);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      const data = await response.json();
      console.log("KIE API Response:", data);

      if (data.code !== 200) {
        throw new Error(`KIE API Error: ${data.msg}`);
      }

      return data;
    } catch (error) {
      console.error("KIE Image Generation API call failed:", error);
      throw error;
    }
  }

  async queryTask(taskId: string): Promise<KieTaskStatus> {
    try {
      const uri = `${baseUrl}/record-info?taskId=${taskId}`;
      
      console.log("KIE Query Task request:", uri);

      const response = await fetch(uri, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("KIE Query API Error:", response.status, errorText);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      const data = await response.json();
      console.log("KIE Query API Response:", data);

      if (data.code !== 200) {
        throw new Error(`KIE Query API Error: ${data.msg}`);
      }

      return data;
    } catch (error) {
      console.error("KIE Query Task API call failed:", error);
      throw error;
    }
  }

  async getDownloadUrl(imageUrl: string): Promise<KieDownloadResponse> {
    try {
      const uri = `${baseUrl}/download-url`;
      
      console.log("KIE Download URL request:", uri, { imageUrl });

      const response = await fetch(uri, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ imageUrl }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("KIE Download URL API Error:", response.status, errorText);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      const data = await response.json();
      console.log("KIE Download URL API Response:", data);

      if (data.code !== 200) {
        throw new Error(`KIE Download URL API Error: ${data.msg}`);
      }

      return data;
    } catch (error) {
      console.error("KIE Download URL API call failed:", error);
      throw error;
    }
  }
}

export function newClient({
  apiKey,
}: {
  apiKey: string;
}): Promise<KieImageGeneration> {
  return Promise.resolve(new KieImageGeneration(apiKey));
}
