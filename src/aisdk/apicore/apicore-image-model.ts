import type { ImageModelV1, ImageModelV1CallWarning } from "@ai-sdk/provider";
import { ApicoreImageModelId, ApicoreImageSettings } from "./apicore-image-settings";
import type { FetchFunction, Resolvable } from "@ai-sdk/provider-utils";
import { newApicoreClient } from "./client";

interface ApicoreImageModelConfig {
  apiKey: string;
  provider: string;
  baseURL: string;
  headers: Resolvable<Record<string, string | undefined>>;
  fetch?: FetchFunction;
}

export class ApicoreImageModel implements ImageModelV1 {
  readonly specificationVersion = "v1";
  readonly modelId: ApicoreImageModelId;
  readonly settings: ApicoreImageSettings;
  private readonly config: ApicoreImageModelConfig;

  get provider(): string {
    return this.config.provider;
  }

  get maxImagesPerCall(): number {
    return this.settings.maxImagesPerCall ?? 1;
  }

  constructor(
    modelId: ApicoreImageModelId,
    settings: ApicoreImageSettings,
    config: ApicoreImageModelConfig
  ) {
    this.modelId = modelId;
    this.settings = settings;
    this.config = config;
  }

  async doGenerate({
    prompt,
    n,
    size,
    providerOptions,
    abortSignal,
  }: Parameters<ImageModelV1["doGenerate"]>[0]): Promise<
    Awaited<ReturnType<ImageModelV1["doGenerate"]>>
  > {
    const warnings: Array<ImageModelV1CallWarning> = [];
    let images: Array<Uint8Array> = [];
    const currentDate = new Date();

    try {
      if (!this.config.apiKey) {
        throw new Error("ApiCore API key is not set");
      }

      const client = await newApicoreClient({
        apiKey: this.config.apiKey,
      });

      // 获取用户上传的图像 (从providerOptions中)
      const userImage = providerOptions.apicore?.image;
      
      if (!userImage) {
        throw new Error("Image is required for ApiCore image editing");
      }

      const result = await client.editImage({
        model: this.modelId,
        image: userImage as string | Blob | File | Buffer,
        mask: providerOptions.apicore?.mask as string | Blob | File | Buffer | undefined,
        prompt,
        n: n || 1,
        size: size || this.settings.size,
        response_format: this.settings.response_format || "b64_json",
        ...(providerOptions.apicore ?? {}),
      });

      if (!result.success) {
        throw new Error(result.error || "Image generation failed");
      }

      // 处理返回的图片数据
      if (result.data?.data) {
        const responseFormat = (providerOptions.apicore?.response_format as string) || "b64_json";

        if (responseFormat === "url") {
          // URL格式：创建包含URL信息的特殊Uint8Array对象
          images = result.data.data.map((item: any) => {
            if (item.url) {
              // 创建一个特殊的对象，包含URL信息
              const urlData = new Uint8Array(0);
              (urlData as any).url = item.url;
              (urlData as any).revised_prompt = item.revised_prompt;
              return urlData;
            }
            throw new Error("No URL found in response");
          });
        } else {
          // Base64格式：下载并转换为Uint8Array
          const imagePromises = result.data.data.map(async (item: any) => {
            if (item.b64_json) {
              // 如果返回的是base64
              return new Uint8Array(
                Buffer.from(item.b64_json, 'base64')
              );
            } else if (item.url) {
              // 如果返回的是URL，需要下载
              const response = await fetch(item.url);
              return new Uint8Array(await response.arrayBuffer());
            }
            throw new Error("Invalid image data format");
          });

          images = await Promise.all(imagePromises);
        }
      }

      if (abortSignal?.aborted) {
        throw new Error("Operation aborted");
      }

    } catch (error: any) {
      warnings.push({
        type: "other",
        message: error.message,
      });
    }

    if (images.length === 0) {
      warnings.push({
        type: "other",
        message: "No images generated",
      });
    }

    return {
      images,
      warnings,
      response: {
        timestamp: currentDate,
        modelId: this.modelId,
        headers: undefined,
      },
    };
  }
}