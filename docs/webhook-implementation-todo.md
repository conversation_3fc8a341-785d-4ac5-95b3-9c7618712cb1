# Pet Portrait Webhook 实现 TODO

## 📋 实施清单

### Phase 1: 数据库准备
- [ ] **1.1** 在 `src/db/schema.ts` 中添加 `petPortraitTasks` 表定义
- [ ] **1.2** 生成数据库迁移文件
- [ ] **1.3** 执行数据库迁移
- [ ] **1.4** 创建 `src/models/pet-portrait-task.ts` 模型文件

### Phase 2: API 路由实现
- [ ] **2.1** 修改 `src/app/api/pet-portrait/generate/route.ts` 为异步模式
  - [ ] 生成唯一 task_id
  - [ ] 保存任务到数据库
  - [ ] 提交到 KIE (带 callBackUrl)
  - [ ] 立即返回 task_id
- [ ] **2.2** 创建 `src/app/api/pet-portrait/status/[id]/route.ts` 状态查询 API
- [ ] **2.3** 创建 `src/app/api/webhooks/kie/route.ts` webhook 处理器

### Phase 3: KIE SDK 修改
- [ ] **3.1** 修改 `src/aisdk/kie/kie-image-model.ts` 支持双模式
  - [ ] 添加 callBackUrl 参数支持
  - [ ] 保留轮询逻辑作为备选方案
  - [ ] 添加模式选择逻辑 (webhook 优先，轮询备选)
  - [ ] 立即返回任务信息 (webhook 模式)
- [ ] **3.2** 更新 KIE API 调用参数

### Phase 4: 前端修改
- [ ] **4.1** 修改 `src/app/[locale]/(default)/test/page.tsx` 提交逻辑
  - [ ] 改为异步提交模式
  - [ ] 添加任务状态轮询
  - [ ] 添加进度显示组件
- [ ] **4.2** 创建任务状态显示组件
- [ ] **4.3** 添加错误处理和重试机制

### Phase 5: 环境配置
- [ ] **5.1** 添加 `NEXT_PUBLIC_BASE_URL` 环境变量
- [ ] **5.2** 确认 KIE webhook URL 配置
- [ ] **5.3** 测试 webhook 可达性

### Phase 6: 测试验证
- [ ] **6.1** 本地开发环境测试
- [ ] **6.2** 部署到 Cloudflare Workers 测试
- [ ] **6.3** 端到端功能测试
- [ ] **6.4** 错误场景测试

## 🔧 具体实现细节

### 数据库表结构
```sql
CREATE TABLE pet_portrait_tasks (
  id INTEGER PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
  task_id VARCHAR(255) NOT NULL UNIQUE,
  provider_task_id VARCHAR(255),
  provider VARCHAR(50) NOT NULL,
  mode VARCHAR(50) NOT NULL DEFAULT 'webhook',  -- 'webhook' 或 'polling'
  status VARCHAR(50) NOT NULL DEFAULT 'pending',
  input_data JSONB NOT NULL,
  result_urls JSONB,
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE
);
```

### API 端点设计
- `POST /api/pet-portrait/generate` - 异步提交任务
- `GET /api/pet-portrait/status/:id` - 查询任务状态
- `POST /api/webhooks/kie` - KIE webhook 回调

### 双模式支持
```typescript
// Webhook 模式 (优先)
1. 提交任务时设置 callBackUrl
2. 立即返回 task_id
3. 等待 webhook 回调更新状态

// 轮询模式 (备选)
1. 提交任务不设置 callBackUrl
2. 立即返回 task_id
3. 后台定时任务轮询 KIE API 更新状态
```

### 状态流转
```
pending → completed/failed
```

### 前端轮询策略
- 提交后立即开始轮询
- 每 10 秒轮询一次
- 完成或失败后停止轮询

## 🎯 验收标准

### 功能验收
- [ ] 用户提交任务后立即得到 task_id
- [ ] 前端能正确显示 "处理中" 状态
- [ ] KIE 完成后能通过 webhook 更新状态
- [ ] 前端能正确显示最终结果或错误信息
- [ ] 整个流程不会出现超时错误

### 性能验收
- [ ] 任务提交响应时间 < 3 秒
- [ ] 状态查询响应时间 < 1 秒
- [ ] Webhook 处理响应时间 < 2 秒

### 用户体验验收
- [ ] 提交后有明确的进度提示
- [ ] 错误信息友好易懂
- [ ] 页面刷新后能恢复任务状态

## 🚨 注意事项

1. **Webhook URL**: 确保 Cloudflare Workers 的 webhook URL 可以被 KIE 访问
2. **数据库连接**: 确认 Hyperdrive 连接配置正确
3. **错误处理**: 添加完整的错误处理和日志记录
4. **安全性**: 考虑添加 webhook 签名验证（可选）
5. **清理机制**: 考虑定期清理过期任务数据（后续优化）
6. **模式降级**: Webhook 失败时自动降级到轮询模式
7. **轮询任务**: 需要后台定时任务处理轮询模式的任务

## 📅 预估时间

- **Phase 1**: 2-3 小时
- **Phase 2**: 4-5 小时  
- **Phase 3**: 2-3 小时
- **Phase 4**: 3-4 小时
- **Phase 5**: 1 小时
- **Phase 6**: 2-3 小时

**总计**: 约 14-19 小时

## 🔄 实施顺序

建议按照 Phase 顺序依次实施，每个 Phase 完成后进行基本测试，确保功能正常后再进入下一个 Phase。

---

*准备开始实施时，请确认所有环境变量和依赖都已配置完成*
