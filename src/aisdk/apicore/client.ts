export interface ApicoreConfig {
  apiKey: string;
}

export interface ApicoreResponse {
  success: boolean;
  data?: {
    created: number;
    data: Array<{
      url?: string;
      b64_json?: string;
    }>;
    usage?: {
      prompt_tokens: number;
      completion_tokens: number;
      total_tokens: number;
    };
  };
  error?: string;
  request_id?: string;
}

export const baseUrl = "https://api.apicore.ai";

class ApicoreClient {
  private apiKey: string;

  constructor(config: ApicoreConfig) {
    this.apiKey = config.apiKey;
  }

  async generateImage({
    model = "gpt-4o-image",
    prompt,
    n = 1,
    size = "1024x1024",
    response_format = "b64_json",
    ...params
  }: {
    model?: string;
    prompt: string;
    n?: number;
    size?: string;
    response_format?: "url" | "b64_json";
    [key: string]: any;
  }): Promise<ApicoreResponse> {
    try {
      const uri = `${baseUrl}/v1/images/generations`;

      console.log("ApiCore Image Generation request:", uri);
      console.log("Request parameters:", { model, prompt: prompt.substring(0, 100) + "...", n, size });

      const requestBody = {
        model,
        prompt,
        n,
        size,
        response_format,
        ...params,
      };

      const response = await fetch(uri, {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${this.apiKey}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("ApiCore API Error:", response.status, errorText);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      const data = await response.json();
      console.log("ApiCore API Response:", data);

      return {
        success: true,
        data,
        request_id: data.request_id || `req_${Date.now()}`,
      };
    } catch (error) {
      console.error("ApiCore Image Generation API call failed:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  async editImage({
    model = "gpt-4o-image",
    image,
    mask,
    prompt,
    n = 1,
    size = "1024x1024",
    response_format = "b64_json",
    ...params
  }: {
    model?: string;
    image: File | Blob | Buffer | string; // 支持多种图像输入格式
    mask?: File | Blob | Buffer | string; // 可选的遮罩
    prompt: string;
    n?: number;
    size?: string;
    response_format?: "url" | "b64_json";
    [key: string]: any;
  }): Promise<ApicoreResponse> {
    try {
      const uri = `${baseUrl}/v1/images/edits`;
      console.log("Request URL:", uri);
      
      // 创建FormData用于图像上传
      const formData = new FormData();
      formData.append("model", model);
      formData.append("prompt", prompt);
      formData.append("n", n.toString());
      formData.append("size", size);
      formData.append("response_format", response_format);
      
      console.log("Request parameters:", { model, prompt, n, size, response_format });
      
      // 处理图像文件
      let imageType = "unknown";
      let imageSize = 0;
      
      if (image instanceof File || image instanceof Blob) {
        formData.append("image", image);
        imageType = image instanceof File ? `File (${image.name})` : "Blob";
        imageSize = image.size;
      } else if (image instanceof Buffer) {
        const blob = new Blob([image], { type: "image/png" });
        formData.append("image", blob);
        imageType = "Buffer -> Blob";
        imageSize = image.length;
      } else if (typeof image === "string") {
        // 如果是base64字符串，转换为Blob
        const base64Data = image.replace(/^data:image\/[a-z]+;base64,/, "");
        const binaryData = Buffer.from(base64Data, "base64");
        const blob = new Blob([binaryData], { type: "image/png" });
        formData.append("image", blob);
        imageType = "base64 -> Blob";
        imageSize = binaryData.length;
      }
      
      console.log("Image info:", { imageType, imageSize });

      // 处理可选的遮罩
      if (mask) {
        if (mask instanceof File || mask instanceof Blob) {
          formData.append("mask", mask);
        } else if (mask instanceof Buffer) {
          const blob = new Blob([mask], { type: "image/png" });
          formData.append("mask", blob);
        }
        console.log("- mask: provided");
      } else {
        console.log("- mask: not provided");
      }
      
      // 添加其他参数
      if (Object.keys(params).length > 0) {
        console.log("Additional parameters:", params);
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined) {
            formData.append(key, String(value));
          }
        });
      }

      console.log("Start sending request to:", uri);
      const startTime = Date.now();

      const response = await fetch(uri, {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${this.apiKey}`,
          // 不设置Content-Type，让浏览器自动设置multipart/form-data boundary
        },
        body: formData,
      });

      const requestTime = Date.now() - startTime;
      console.log("Response received:", response.status, response.statusText);
      console.log("Request time:", requestTime + "ms");


      if (!response.ok) {
        const errorText = await response.text();
        console.log("Error Response:", response.status, response.statusText);
        console.log("Error Body:", errorText);
        
        // 尝试解析错误响应
        let errorDetails = "Unknown error";
        try {
          const errorJson = JSON.parse(errorText);
          errorDetails = JSON.stringify(errorJson, null, 2);
          console.log("- Parsed Error:", errorDetails);
        } catch {
          errorDetails = errorText;
          console.log("- Raw Error Text:", errorText);
        }
        
        throw new Error(`HTTP ${response.status} ${response.statusText}: ${errorDetails}`);
      }

      const data = await response.json();
      console.log("Success Response:", data);
      
      return {
        success: true,
        data,
      };
    } catch (error) {
      console.log("=== Error Caught ===");
      console.error("Error type:", error?.constructor?.name);
      console.error("Error message:", error instanceof Error ? error.message : String(error));
      
      // 详细错误分析
      if (error instanceof TypeError && error.message.includes('fetch failed')) {
        console.error("Network Error Analysis:");
        console.error("- This is likely a network connectivity issue");
        console.error("- Check if you're behind a proxy/firewall");
        console.error("- Verify the API endpoint URL is correct");
        console.error("- Ensure your internet connection is working");
        
        // 检查错误原因
        if ('cause' in error) {
          console.error("- Error cause:", error.cause);
          if (error.cause && typeof error.cause === 'object' && error.cause !== null && 'code' in error.cause) {
            const cause = error.cause as any;
            console.error("- Error code:", cause.code);
            if (cause.code === 'UND_ERR_SOCKET') {
              console.error("- Socket error detected - likely proxy or network issue");
              console.error("- Try disabling proxy or using different network");
            }
          }
        }
      } else if (error instanceof Error && error.message.includes('HTTP')) {
        console.error("HTTP Error Analysis:");
        console.error("- This is an API response error");
        console.error("- Check API key validity and permissions");
        console.error("- Verify API endpoint and request format");
      }
      
      console.log("=== End Error Debug ===");
      
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }
}

export async function newApicoreClient(config: ApicoreConfig): Promise<ApicoreClient> {
  return new ApicoreClient(config);
}