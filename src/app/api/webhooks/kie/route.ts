import { NextRequest } from "next/server";
import { respData, respErr } from "@/lib/resp";
import { PetPortraitTaskModel, AIProvider, TaskStatus } from "@/models/pet-portrait-task";

export async function POST(req: NextRequest) {
  try {
    console.log("=== KIE Webhook 收到回调 ===");
    
    const body = await req.text();
    console.log("Webhook 原始数据:", body);
    
    let data;
    try {
      data = JSON.parse(body);
    } catch (parseError) {
      console.error("JSON 解析失败:", parseError);
      return respErr("Invalid JSON payload", 400);
    }

    console.log("Webhook 解析后数据:", JSON.stringify(data, null, 2));

    // 根据 KIE 官方文档格式解析
    const { code, msg, data: callbackData } = data;

    if (!callbackData?.taskId) {
      console.error("缺少 taskId");
      return respErr("Missing taskId", 400);
    }

    const taskId = callbackData.taskId;

    // 查找对应的任务
    const task = await PetPortraitTaskModel.findByProviderTaskId(AIProvider.KIE, taskId);
    if (!task) {
      console.error(`未找到对应任务: KIE taskId = ${taskId}`);
      return respErr("Task not found", 404);
    }

    console.log(`找到任务: ${task.task_id} (KIE: ${taskId})`);

    // 根据 code 状态码更新任务状态
    const updateParams: any = {};

    if (code === 200) {
      // 任务成功完成
      console.log("任务成功完成");
      updateParams.status = TaskStatus.Completed;
      updateParams.completed_at = new Date();

      if (callbackData.info?.result_urls && Array.isArray(callbackData.info.result_urls)) {
        updateParams.result_urls = callbackData.info.result_urls;
        console.log(`获得 ${callbackData.info.result_urls.length} 个结果图片:`, callbackData.info.result_urls);
      } else {
        console.warn("响应中没有找到 result_urls");
        updateParams.result_urls = [];
      }

    } else {
      // 任务失败 (400, 451, 500 等)
      console.log(`任务失败 (${code}):`, msg);
      updateParams.status = TaskStatus.Failed;
      updateParams.error_message = `${msg} (Code: ${code})`;
      updateParams.completed_at = new Date();

      // 根据不同错误码记录详细信息
      if (code === 400) {
        console.log("内容违规或参数错误");
      } else if (code === 451) {
        console.log("图片下载失败");
      } else if (code === 500) {
        console.log("服务器内部错误");
      }
    }

    // 更新任务状态
    const updatedTask = await PetPortraitTaskModel.update(task.task_id, updateParams);
    
    if (updatedTask) {
      console.log(`任务 ${task.task_id} 状态已更新为: ${updatedTask.status}`);
    } else {
      console.error(`任务 ${task.task_id} 状态更新失败`);
    }

    return respData({
      message: "Webhook processed successfully",
      task_id: task.task_id,
      status: updateParams.status,
    });

  } catch (error) {
    console.error("KIE Webhook 处理失败:", error);
    return respErr("Webhook processing failed");
  }
}

// 支持 GET 请求用于测试
export async function GET() {
  return respData({
    message: "KIE Webhook endpoint is working",
    timestamp: new Date().toISOString(),
  });
}
