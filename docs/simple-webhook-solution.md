# Pet Portrait Webhook 简化方案

## 🎯 目标

解决当前 KIE AI 等提供商的超时问题，通过 webhook 机制实现异步处理，提升用户体验。

## 🏗️ 简化架构

```
前端提交任务 → API 立即返回 taskId → KIE 处理完成 → Webhook 回调 → 前端轮询获取结果
```

## 📊 最小化数据库设计

### 1. 新增一个简单的任务表

```sql
CREATE TABLE pet_portrait_tasks (
  id INTEGER PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
  task_id VARCHAR(255) NOT NULL UNIQUE,        -- 我们生成的任务ID
  provider_task_id VARCHAR(255),               -- KIE返回的任务ID
  provider VARCHAR(50) NOT NULL,               -- 'kie', 'apicore', 'replicate'
  
  -- 状态管理
  status VARCHAR(50) NOT NULL DEFAULT 'pending', -- 'pending', 'completed', 'failed'
  
  -- 输入输出
  input_data JSONB NOT NULL,                   -- 输入参数
  result_urls JSONB,                           -- 结果图片URLs
  error_message TEXT,                          -- 错误信息
  
  -- 时间戳
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE
);

CREATE INDEX idx_pet_portrait_tasks_task_id ON pet_portrait_tasks(task_id);
CREATE INDEX idx_pet_portrait_tasks_provider_task_id ON pet_portrait_tasks(provider, provider_task_id);
```

## 🛣️ API 路由设计

### 1. 修改现有的 pet-portrait API

```
POST /api/pet-portrait/generate     # 异步提交任务
GET  /api/pet-portrait/status/:id   # 查询任务状态
POST /api/webhooks/kie              # KIE webhook 回调
```

## 🔄 工作流程

### 1. 任务提交流程

```typescript
// POST /api/pet-portrait/generate
1. 接收用户请求
2. 生成唯一 task_id
3. 保存任务到数据库 (status: 'pending')
4. 提交到 KIE (带 callBackUrl)
5. 更新 provider_task_id
6. 立即返回 { task_id, status: 'pending' }
```

### 2. Webhook 处理流程

```typescript
// POST /api/webhooks/kie
1. 接收 KIE 回调
2. 根据 provider_task_id 查找任务
3. 更新任务状态和结果
4. 返回 200 OK
```

### 3. 前端轮询流程

```typescript
// GET /api/pet-portrait/status/:id
1. 前端每10秒轮询一次
2. 返回任务状态和结果
3. 完成后停止轮询
```

## 💻 具体实现

### 1. 数据库模型

```typescript
// src/models/pet-portrait-task.ts
export interface PetPortraitTask {
  id: number;
  task_id: string;
  provider_task_id?: string;
  provider: string;
  status: 'pending' | 'completed' | 'failed';
  input_data: any;
  result_urls?: string[];
  error_message?: string;
  created_at: Date;
  completed_at?: Date;
}

export class PetPortraitTaskModel {
  static async create(data: {
    task_id: string;
    provider: string;
    input_data: any;
  }): Promise<PetPortraitTask>;

  static async findByTaskId(task_id: string): Promise<PetPortraitTask | null>;

  static async findByProviderTaskId(
    provider: string, 
    provider_task_id: string
  ): Promise<PetPortraitTask | null>;

  static async updateStatus(
    task_id: string, 
    updates: {
      status?: string;
      result_urls?: string[];
      error_message?: string;
      completed_at?: Date;
    }
  ): Promise<PetPortraitTask>;
}
```

### 2. 修改 pet-portrait API

```typescript
// src/app/api/pet-portrait/generate/route.ts
export async function POST(req: Request) {
  try {
    // 解析请求参数
    const formData = await req.formData();
    const petType = formData.get("petType") as string;
    const style = formData.get("style") as string;
    const provider = formData.get("provider") as string || "kie";
    const petImage = formData.get("image") as File;

    // 生成任务ID
    const taskId = getUuid();

    // 保存任务到数据库
    await PetPortraitTaskModel.create({
      task_id: taskId,
      provider: provider,
      input_data: {
        petType,
        style,
        provider,
        // ... 其他参数
      }
    });

    // 根据 provider 处理
    if (provider === 'kie') {
      // 上传图片获取URL
      const petImageUrl = await uploadImageToR2(petImage);
      
      // 提交到 KIE
      const kieClient = await newClient({ apiKey: process.env.KIE_API_KEY! });
      const kieTask = await kieClient.createTask({
        prompt: `Transform this ${petType} photo into a beautiful ${style}...`,
        filesUrl: [petImageUrl],
        size: "1:1",
        callBackUrl: `${process.env.NEXT_PUBLIC_WEB_URL}/api/webhooks/kie`,
      });

      // 更新 provider_task_id
      await PetPortraitTaskModel.updateStatus(taskId, {
        provider_task_id: kieTask.data.taskId,
      });
    }

    // 立即返回任务ID
    return respData({
      task_id: taskId,
      status: 'pending',
      message: '任务已提交，正在处理中...'
    });

  } catch (error) {
    console.error("Pet portrait generation failed:", error);
    return respErr("Failed to create task");
  }
}
```

### 3. 任务状态查询 API

```typescript
// src/app/api/pet-portrait/status/[id]/route.ts
export async function GET(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const task = await PetPortraitTaskModel.findByTaskId(params.id);
    
    if (!task) {
      return respErr("Task not found", 404);
    }

    return respData({
      task_id: task.task_id,
      status: task.status,
      result_urls: task.result_urls,
      error_message: task.error_message,
      created_at: task.created_at,
      completed_at: task.completed_at,
    });

  } catch (error) {
    console.error("Get task status failed:", error);
    return respErr("Failed to get task status");
  }
}
```

### 4. KIE Webhook 处理

```typescript
// src/app/api/webhooks/kie/route.ts
export async function POST(req: Request) {
  try {
    const data = await req.json();
    const { taskId, successFlag, response, errorMessage } = data;

    console.log("KIE webhook received:", data);

    // 查找对应任务
    const task = await PetPortraitTaskModel.findByProviderTaskId('kie', taskId);
    if (!task) {
      console.error("Task not found for KIE taskId:", taskId);
      return respErr("Task not found", 404);
    }

    // 更新任务状态
    const updates: any = {
      completed_at: new Date(),
    };

    if (successFlag === 1) {
      // 成功
      updates.status = 'completed';
      updates.result_urls = response?.resultUrls || [];
    } else if (successFlag === 2) {
      // 失败
      updates.status = 'failed';
      updates.error_message = errorMessage || 'KIE generation failed';
    }

    await PetPortraitTaskModel.updateStatus(task.task_id, updates);

    console.log(`Task ${task.task_id} updated to status: ${updates.status}`);

    return respData({ success: true });

  } catch (error) {
    console.error("KIE webhook processing failed:", error);
    return respErr("Webhook processing failed");
  }
}
```

### 5. 前端修改

```typescript
// 前端提交逻辑
const handleSubmit = async () => {
  try {
    const response = await fetch('/api/pet-portrait/generate', {
      method: 'POST',
      body: formData,
    });

    const result = await response.json();
    
    if (result.success) {
      // 开始轮询任务状态
      pollTaskStatus(result.data.task_id);
    }
  } catch (error) {
    console.error('Submit failed:', error);
  }
};

// 轮询任务状态
const pollTaskStatus = async (taskId: string) => {
  const poll = async () => {
    try {
      const response = await fetch(`/api/pet-portrait/status/${taskId}`);
      const result = await response.json();
      
      if (result.success) {
        const task = result.data;
        
        if (task.status === 'completed') {
          // 显示结果
          setResult(task.result_urls);
          return; // 停止轮询
        } else if (task.status === 'failed') {
          // 显示错误
          setError(task.error_message);
          return; // 停止轮询
        }
        
        // 继续轮询
        setTimeout(poll, 10000); // 10秒后再次轮询
      }
    } catch (error) {
      console.error('Poll failed:', error);
      setTimeout(poll, 10000); // 出错后也继续轮询
    }
  };

  poll(); // 开始轮询
};
```

## 🔧 环境配置

```bash
# .env
# Webhook 基础URL
NEXT_PUBLIC_BASE_URL=https://your-domain.workers.dev

# KIE API Key
KIE_API_KEY=your_kie_api_key
```

## ✅ 优势

1. **简单直接**：最小化的数据库设计，只存储必要信息
2. **立即响应**：用户提交后立即得到反馈
3. **无超时问题**：通过 webhook 异步处理，不受 Workers 超时限制
4. **易于扩展**：可以轻松添加其他 provider 的 webhook 支持
5. **用户体验好**：实时进度反馈，不会卡死

## 🚀 实施步骤

1. **创建数据库表**：添加 `pet_portrait_tasks` 表
2. **修改现有 API**：改为异步模式
3. **添加状态查询 API**：供前端轮询使用
4. **添加 webhook 处理**：接收 KIE 回调
5. **修改前端逻辑**：改为提交+轮询模式

这个方案专注解决超时问题，后续可以根据需要逐步添加用户系统和积分系统的集成。
