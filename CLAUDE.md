# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an AI SaaS application template called "ShipAny Template One" built with Next.js 15. It's designed to help entrepreneurs ship AI startups quickly with features like user authentication, payment processing, multi-language support, and an admin dashboard.

## Architecture

### Core Stack
- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: NextAuth.js 5.0 (beta)
- **Styling**: Tailwind CSS + shadcn/ui components
- **AI Integration**: Vercel AI SDK with multiple providers (OpenAI, Replicate, DeepSeek, etc.)
- **Payment**: Stripe and Creem integrations
- **Internationalization**: next-intl

### Key Directory Structure
- `src/app/` - Next.js app router pages and API routes
- `src/components/` - Reusable UI components organized by feature
- `src/db/` - Database schema, migrations, and configuration
- `src/i18n/` - Internationalization setup and translations
- `src/models/` - Data models and types
- `src/services/` - Business logic and external service integrations
- `src/aisdk/` - Custom AI SDK providers and utilities

### Multi-tenancy Structure
The app uses locale-based routing with admin and console sections:
- `[locale]/(admin)/` - Admin dashboard for managing users, posts, orders
- `[locale]/(console)/` - User console for API keys, credits, orders
- `[locale]/(default)/` - Public pages (landing, pricing, showcase)
- `[locale]/(docs)/` - Documentation with Fumadocs

## Development Commands

### Essential Commands
- `pnpm dev` - Start development server with Turbopack
- `pnpm build` - Build for production
- `pnpm lint` - Run ESLint
- `pnpm start` - Start production server

### Database Commands
- `pnpm db:generate` - Generate migrations from schema changes
- `pnpm db:migrate` - Run pending migrations
- `pnpm db:studio` - Open Drizzle Studio for database management
- `pnpm db:push` - Push schema changes directly to database (dev only)

### Deployment Commands
- `pnpm cf:preview` - Preview Cloudflare deployment
- `pnpm cf:deploy` - Deploy to Cloudflare Pages
- `docker:build` - Build Docker image

## Environment Setup

Copy `.env.example` to create your environment file:
- Development: `.env.development`
- Production: `.env.production`

## Key Integrations

### AI Providers
The `src/aisdk/` directory contains custom AI provider implementations:
- Kling video generation provider
- OpenAI, Replicate, DeepSeek integrations
- Custom video model abstractions

### Payment Systems
- **Stripe**: Full integration in `src/integrations/stripe/`
- **Creem**: Alternative payment provider in `src/integrations/creem/`

### Authentication
NextAuth.js configuration supports:
- Google OAuth
- Email/password
- Custom providers

## Deployment Options

### Vercel (Primary)
Standard Next.js deployment with Vercel integration.

### Cloudflare Pages
Use the `cloudflare` branch for Cloudflare-specific optimizations:
- `git checkout cloudflare` for existing projects
- Configure `wrangler.toml` from example
- Use `cf:deploy` commands

### Docker
Dockerfile included for containerized deployments.

## Testing and Quality

When making changes:
1. Run `pnpm lint` to check code style
2. Run `pnpm build` to verify production build
3. Test database changes with `pnpm db:studio`

## Component Development

Follow these patterns when creating new components:
- Use shadcn/ui components from `src/components/ui/`
- Block components go in `src/components/blocks/`
- Maintain TypeScript types in `src/types/`
- Follow existing internationalization patterns in `src/i18n/`