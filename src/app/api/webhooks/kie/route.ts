import { NextRequest } from "next/server";
import { respData, respErr } from "@/lib/resp";
import { PetPortraitTaskModel, AIProvider, TaskStatus } from "@/models/pet-portrait-task";

export async function POST(req: NextRequest) {
  try {
    console.log("=== KIE Webhook 收到回调 ===");
    
    const body = await req.text();
    console.log("Webhook 原始数据:", body);
    
    let data;
    try {
      data = JSON.parse(body);
    } catch (parseError) {
      console.error("JSON 解析失败:", parseError);
      return respErr("Invalid JSON payload", 400);
    }

    console.log("Webhook 解析后数据:", JSON.stringify(data, null, 2));

    const { taskId, successFlag, response, errorMessage, progress } = data;

    if (!taskId) {
      console.error("缺少 taskId");
      return respErr("Missing taskId", 400);
    }

    // 查找对应的任务
    const task = await PetPortraitTaskModel.findByProviderTaskId(AIProvider.KIE, taskId);
    if (!task) {
      console.error(`未找到对应任务: KIE taskId = ${taskId}`);
      return respErr("Task not found", 404);
    }

    console.log(`找到任务: ${task.task_id} (KIE: ${taskId})`);

    // 根据 successFlag 更新任务状态
    const updateParams: any = {};

    if (successFlag === 1) {
      // 任务成功完成
      console.log("任务成功完成");
      updateParams.status = TaskStatus.Completed;
      updateParams.completed_at = new Date();
      
      if (response?.resultUrls && Array.isArray(response.resultUrls)) {
        updateParams.result_urls = response.resultUrls;
        console.log(`获得 ${response.resultUrls.length} 个结果图片:`, response.resultUrls);
      } else {
        console.warn("响应中没有找到 resultUrls");
        updateParams.result_urls = [];
      }
      
    } else if (successFlag === 2) {
      // 任务失败
      console.log("任务失败:", errorMessage);
      updateParams.status = TaskStatus.Failed;
      updateParams.error_message = errorMessage || "KIE generation failed";
      updateParams.completed_at = new Date();
      
    } else if (successFlag === 0) {
      // 任务进行中 (如果 KIE 支持进度回调)
      console.log(`任务进行中, 进度: ${progress}`);
      // 这里可以添加进度更新逻辑，但当前简化版本不处理
      return respData({ 
        message: "Progress update received",
        task_id: task.task_id,
        progress: progress 
      });
      
    } else {
      console.warn(`未知的 successFlag: ${successFlag}`);
      return respData({ 
        message: "Unknown status received",
        task_id: task.task_id 
      });
    }

    // 更新任务状态
    const updatedTask = await PetPortraitTaskModel.update(task.task_id, updateParams);
    
    if (updatedTask) {
      console.log(`任务 ${task.task_id} 状态已更新为: ${updatedTask.status}`);
    } else {
      console.error(`任务 ${task.task_id} 状态更新失败`);
    }

    return respData({
      message: "Webhook processed successfully",
      task_id: task.task_id,
      status: updateParams.status,
    });

  } catch (error) {
    console.error("KIE Webhook 处理失败:", error);
    return respErr("Webhook processing failed");
  }
}

// 支持 GET 请求用于测试
export async function GET() {
  return respData({
    message: "KIE Webhook endpoint is working",
    timestamp: new Date().toISOString(),
  });
}
