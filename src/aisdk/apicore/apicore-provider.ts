import type { FetchFunction } from "@ai-sdk/provider-utils";
import { loadSetting } from "@ai-sdk/provider-utils";
import { ApicoreImageModel } from "./apicore-image-model";
import { ApicoreImageModelId, ApicoreImageSettings } from "./apicore-image-settings";

export interface ApicoreProviderSettings {
  apiKey?: string;
  baseURL?: string;
  headers?: Record<string, string>;
  fetch?: FetchFunction;
}

export interface ApicoreProvider {
  image(
    modelId: ApicoreImageModelId,
    settings?: ApicoreImageSettings
  ): ApicoreImageModel;
}

export function createApicore(
  options: ApicoreProviderSettings = {}
): ApicoreProvider {
  const loadApiKey = () =>
    loadSetting({
      settingValue: options.apiKey,
      settingName: "apiKey",
      environmentVariableName: "APICORE_API_KEY",
      description: "ApiCore API key",
    });

  return {
    image: (modelId: ApicoreImageModelId, settings?: ApicoreImageSettings) => {
      return new ApicoreImageModel(modelId, settings ?? {}, {
        apiKey: loadApiKey(),
        provider: "apicore",
        baseURL: options.baseURL ?? "https://api.apicore.ai",
        headers: {
          ...options.headers,
        },
        fetch: options.fetch,
      });
    },
  };
}

export const apicore = createApicore();