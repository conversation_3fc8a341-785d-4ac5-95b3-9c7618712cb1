import { experimental_generateImage as generateImage } from "ai";
import { apicore } from "@/aisdk/apicore";
import { respData, respErr } from "@/lib/resp";
import { newStorage } from "@/lib/storage";
import { getUuid } from "@/lib/hash";

export async function POST(req: Request) {
  try {
    // 支持FormData (图像上传) 和 JSON 两种格式
    let petType, style, description, model, size, petImage;
    
    const contentType = req.headers.get("content-type");
    
    if (contentType?.includes("multipart/form-data")) {
      // 处理FormData格式
      const formData = await req.formData();
      petType = formData.get("petType") as string;
      style = (formData.get("style") as string) || "realistic portrait";
      description = (formData.get("description") as string) || "";
      model = (formData.get("model") as string) || "gpt-4o-image";
      size = (formData.get("size") as string) || "1024x1024";
      petImage = formData.get("image") as File;
    } else {
      // 处理JSON格式 (base64图像)
      const body = await req.json();
      petType = body.petType;
      style = body.style || "realistic portrait";
      description = body.description || "";
      model = body.model || "gpt-4o-image";
      size = body.size || "1024x1024";
      petImage = body.image; // base64 string
    }

    if (!petType || !petImage) {
      return respErr("Pet type and image are required");
    }

    // 构建宠物肖像专用prompt
    const prompt = `Transform this ${petType} photo into a beautiful ${style}. ${description}. High quality, detailed, professional artistic portrait, studio lighting, sharp focus, artistic composition.`;

    const { images, warnings } = await generateImage({
      model: apicore.image(model),
      prompt,
      n: 1,
      providerOptions: {
        apicore: {
          image: petImage, // 用户上传的宠物图片
          response_format: "url",
          size,
        },
      },
    });

    if (warnings.length > 0) {
      console.log("Pet portrait generation warnings:", warnings);
      return respErr("Pet portrait generation failed");
    }

    // 上传到存储
    // const storage = newStorage();
    // const batch = getUuid();
    // const filename = `pet_portrait_${batch}.png`;
    // const key = `pet-portraits/${filename}`;

    // try {
    //   const uploadResult = await storage.uploadFile({
    //     body: Buffer.from(images[0].base64, "base64"),
    //     key,
    //     contentType: "image/png",
    //     disposition: "inline",
    //   });

    //   return respData({
    //     imageUrl: uploadResult.url,
    //     filename,
    //     prompt,
    //     model,
    //     petType,
    //     style,
    //   });
    // } catch (err) {
    //   console.log("Upload file failed:", err);
    //   return respData({
    //     base64: images[0].base64,
    //     prompt,
    //     model,
    //     petType,
    //     style,
    //     filename,
    //   });
    // }
  } catch (err) {
    console.error("Pet portrait generation failed:", err);
    return respErr("Failed to generate pet portrait");
  }
}