export type KieImageModelId = 
  | "gpt-4o-image"
  | "gpt-4o-image-enhanced";

export interface KieImageSettings {
  /**
   * Maximum number of images per call.
   */
  maxImagesPerCall?: number;

  /**
   * Image aspect ratio.
   */
  size?: "1:1" | "3:2" | "2:3";

  /**
   * Enable prompt enhancement for better results.
   */
  isEnhance?: boolean;

  /**
   * Enable fallback mechanism for service reliability.
   */
  enableFallback?: boolean;

  /**
   * Fallback model to use when main model is unavailable.
   */
  fallbackModel?: "FLUX_MAX" | "GPT_IMAGE_1";

  /**
   * Number of image variants to generate (1, 2, or 4).
   */
  nVariants?: number;

  /**
   * Upload to China region.
   */
  uploadCn?: boolean;
}
