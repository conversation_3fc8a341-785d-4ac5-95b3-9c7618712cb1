import { experimental_generateImage as generateImage } from "ai";
import { apicore } from "@/aisdk/apicore";
import { respData, respErr } from "@/lib/resp";
import { newStorage } from "@/lib/storage";
import { getUuid } from "@/lib/hash";

export async function POST(req: Request) {
  try {
    // 支持FormData (图像上传) 和 JSON 两种格式
    let petType, style, description, model, size, petImage;
    
    const contentType = req.headers.get("content-type");
    
    if (contentType?.includes("multipart/form-data")) {
      // 处理FormData格式
      const formData = await req.formData();
      petType = formData.get("petType") as string;
      style = (formData.get("style") as string) || "realistic portrait";
      description = (formData.get("description") as string) || "";
      model = (formData.get("model") as string) || "gpt-4o-image";
      size = (formData.get("size") as string) || "1024x1024";
      petImage = formData.get("image") as File;
    } else {
      // 处理JSON格式 (base64图像)
      const body = await req.json();
      petType = body.petType;
      style = body.style || "realistic portrait";
      description = body.description || "";
      model = body.model || "gpt-4o-image";
      size = body.size || "1024x1024";
      petImage = body.image; // base64 string
    }

    if (!petType || !petImage) {
      return respErr("Pet type and image are required");
    }

    // 构建宠物肖像专用prompt
    const prompt = `Transform this ${petType} photo into a beautiful ${style}. ${description}. High quality, detailed, professional artistic portrait, studio lighting, sharp focus, artistic composition.`;

    const { images, warnings } = await generateImage({
      model: apicore.image(model),
      prompt,
      n: 1,
      providerOptions: {
        apicore: {
          image: petImage, // 用户上传的宠物图片
          response_format: "url",
          size,
        },
      },
    });

    if (warnings.length > 0) {
      console.log("Pet portrait generation warnings:", warnings);
      return respErr("Pet portrait generation failed");
    }

    if (images.length === 0) {
      return respErr("No images generated");
    }

    // 处理返回的图片数据
    console.log("Generated images:", images.length);
    console.log("Generated images:", images);

    const imageResults = images.map((image, index) => {
      const imageData = image as any;

      // 检查是否是URL格式（我们在apicore-image-model中设置的特殊格式）
      if (imageData.url) {
        return {
          url: imageData.url,
          revised_prompt: imageData.revised_prompt,
          type: 'url',
          index,
        };
      } else if (image.base64) {
        // Base64格式
        return {
          url: `data:image/png;base64,${image.base64}`,
          type: 'base64',
          index,
        };
      } else {
        console.warn(`Image ${index} has no URL or base64 data:`, imageData);
        return {
          url: null,
          type: 'unknown',
          index,
        };
      }
    });

    return respData({
      images: imageResults,
      imageUrl: imageResults[0]?.url, // 兼容性：返回第一张图片的URL
      prompt,
      model,
      petType,
      style,
      size,
      count: images.length,
    });
  } catch (err) {
    console.error("Pet portrait generation failed:", err);
    return respErr("Failed to generate pet portrait");
  }
}