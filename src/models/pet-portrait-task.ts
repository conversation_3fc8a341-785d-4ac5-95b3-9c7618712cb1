import { petPortraitTasks } from "@/db/schema";
import { db } from "@/db";
import { eq, and, desc } from "drizzle-orm";
import { getUuid } from "@/lib/hash";

export enum TaskStatus {
  Pending = "pending",
  Completed = "completed",
  Failed = "failed",
}

export enum TaskMode {
  Webhook = "webhook",
  Polling = "polling",
}

export enum AIProvider {
  ApiCore = "apicore",
  Replicate = "replicate",
  KIE = "kie",
}

export interface PetPortraitTask {
  id: number;
  task_id: string;
  provider_task_id?: string;
  provider: string;
  mode: string;
  status: string;
  input_data: string; // JSON string
  result_urls?: string; // JSON string
  error_message?: string;
  created_at: Date;
  completed_at?: Date;
}

export interface CreateTaskParams {
  provider: AIProvider;
  mode?: TaskMode;
  input_data: Record<string, any>;
}

export interface UpdateTaskParams {
  provider_task_id?: string;
  status?: TaskStatus;
  result_urls?: string[];
  error_message?: string;
  completed_at?: Date;
}

export class PetPortraitTaskModel {
  /**
   * 创建新任务
   */
  static async create(params: CreateTaskParams): Promise<PetPortraitTask> {
    const taskId = getUuid();
    
    const [task] = await db().insert(petPortraitTasks).values({
      task_id: taskId,
      provider: params.provider,
      mode: params.mode || TaskMode.Webhook,
      status: TaskStatus.Pending,
      input_data: typeof params.input_data === 'string'
        ? params.input_data
        : JSON.stringify(params.input_data || {}),
    }).returning();

    return task as PetPortraitTask;
  }

  /**
   * 根据 task_id 查找任务
   */
  static async findByTaskId(task_id: string): Promise<PetPortraitTask | null> {
    const [task] = await db()
      .select()
      .from(petPortraitTasks)
      .where(eq(petPortraitTasks.task_id, task_id));
    
    return task ? (task as PetPortraitTask) : null;
  }

  /**
   * 根据 provider 和 provider_task_id 查找任务
   */
  static async findByProviderTaskId(
    provider: AIProvider, 
    provider_task_id: string
  ): Promise<PetPortraitTask | null> {
    const [task] = await db()
      .select()
      .from(petPortraitTasks)
      .where(
        and(
          eq(petPortraitTasks.provider, provider),
          eq(petPortraitTasks.provider_task_id, provider_task_id)
        )
      );
    
    return task ? (task as PetPortraitTask) : null;
  }

  /**
   * 获取指定状态和模式的任务列表（用于轮询处理）
   */
  static async findByStatusAndMode(
    status: TaskStatus,
    mode: TaskMode,
    limit = 50
  ): Promise<PetPortraitTask[]> {
    const tasks = await db()
      .select()
      .from(petPortraitTasks)
      .where(
        and(
          eq(petPortraitTasks.status, status),
          eq(petPortraitTasks.mode, mode)
        )
      )
      .orderBy(desc(petPortraitTasks.created_at))
      .limit(limit);

    return tasks as PetPortraitTask[];
  }

  /**
   * 更新任务
   */
  static async update(task_id: string, params: UpdateTaskParams): Promise<PetPortraitTask | null> {
    const updateData: any = {};
    
    if (params.provider_task_id !== undefined) {
      updateData.provider_task_id = params.provider_task_id;
    }
    if (params.status !== undefined) {
      updateData.status = params.status;
    }
    if (params.result_urls !== undefined) {
      updateData.result_urls = JSON.stringify(params.result_urls);
    }
    if (params.error_message !== undefined) {
      updateData.error_message = params.error_message;
    }
    if (params.completed_at !== undefined) {
      updateData.completed_at = params.completed_at;
    }

    const [task] = await db()
      .update(petPortraitTasks)
      .set(updateData)
      .where(eq(petPortraitTasks.task_id, task_id))
      .returning();

    return task ? (task as PetPortraitTask) : null;
  }

  /**
   * 解析 input_data JSON 字符串
   */
  static parseInputData(task: PetPortraitTask): Record<string, any> {
    try {
      return JSON.parse(task.input_data);
    } catch (error) {
      console.error("Failed to parse input_data:", error);
      return {};
    }
  }

  /**
   * 解析 result_urls JSON 字符串
   */
  static parseResultUrls(task: PetPortraitTask): string[] {
    if (!task.result_urls) return [];
    
    try {
      return JSON.parse(task.result_urls);
    } catch (error) {
      console.error("Failed to parse result_urls:", error);
      return [];
    }
  }

  /**
   * 检查任务是否已完成（成功或失败）
   */
  static isCompleted(task: PetPortraitTask): boolean {
    return task.status === TaskStatus.Completed || task.status === TaskStatus.Failed;
  }

  /**
   * 检查任务是否超时（创建超过指定时间仍未完成）
   */
  static isTimeout(task: PetPortraitTask, timeoutMinutes = 10): boolean {
    if (this.isCompleted(task)) return false;
    
    const now = new Date();
    const createdAt = new Date(task.created_at);
    const diffMinutes = (now.getTime() - createdAt.getTime()) / (1000 * 60);
    
    return diffMinutes > timeoutMinutes;
  }
}
