import { NextRequest } from "next/server";
import { respData, respErr } from "@/lib/resp";
import { PetPortraitTaskModel } from "@/models/pet-portrait-task";

export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const taskId = params.id;
    
    if (!taskId) {
      return respErr("Task ID is required", 400);
    }

    console.log(`查询任务状态: ${taskId}`);

    // 查找任务
    const task = await PetPortraitTaskModel.findByTaskId(taskId);
    
    if (!task) {
      return respErr("Task not found", 404);
    }

    // 解析输入数据和结果URLs
    const inputData = PetPortraitTaskModel.parseInputData(task);
    const resultUrls = PetPortraitTaskModel.parseResultUrls(task);

    // 返回任务状态
    return respData({
      task_id: task.task_id,
      provider: task.provider,
      mode: task.mode,
      status: task.status,
      input_data: inputData,
      result_urls: resultUrls,
      error_message: task.error_message,
      created_at: task.created_at,
      completed_at: task.completed_at,
      is_completed: PetPortraitTaskModel.isCompleted(task),
      is_timeout: PetPortraitTaskModel.isTimeout(task),
    });

  } catch (error) {
    console.error("Get task status failed:", error);
    return respErr("Failed to get task status");
  }
}
