# Pet Portrait 异步架构技术实现指南

## 📋 实现概述

本文档详细描述了 Pet Portrait 异步架构的具体技术实现，包括代码结构、API 设计、数据库操作等。

## 🗄️ 数据库实现

### 1. Schema 定义

```typescript
// src/db/schema.ts - 新增表定义

export const aiTasks = pgTable("ai_tasks", {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  uuid: varchar({ length: 255 }).notNull().unique(),
  user_uuid: varchar({ length: 255 }).notNull(),
  
  // 任务信息
  task_type: varchar({ length: 50 }).notNull(), // 'pet_portrait', 'image_generation'
  provider: varchar({ length: 50 }).notNull(),  // 'apicore', 'replicate', 'kie'
  provider_task_id: varchar({ length: 255 }),
  
  // 状态管理
  status: varchar({ length: 50 }).notNull().default('pending'),
  progress: decimal({ precision: 5, scale: 2 }).default('0.00'),
  
  // 数据存储
  input_params: json().notNull(),
  result_urls: json(),
  result_metadata: json(),
  
  // 错误处理
  error_code: varchar({ length: 50 }),
  error_message: text(),
  
  // 积分相关
  credits_cost: integer().default(0),
  credits_transaction_id: varchar({ length: 255 }),
  
  // 时间戳
  created_at: timestamp({ withTimezone: true }).defaultNow(),
  started_at: timestamp({ withTimezone: true }),
  completed_at: timestamp({ withTimezone: true }),
});

// 索引定义
export const aiTasksUserIndex = index("idx_ai_tasks_user_uuid").on(aiTasks.user_uuid);
export const aiTasksStatusIndex = index("idx_ai_tasks_status").on(aiTasks.status);
export const aiTasksProviderIndex = index("idx_ai_tasks_provider_task_id").on(
  aiTasks.provider, 
  aiTasks.provider_task_id
);
```

### 2. 数据库迁移

```sql
-- src/db/migrations/xxxx_add_ai_tasks.sql

CREATE TABLE ai_tasks (
  id INTEGER PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
  uuid VARCHAR(255) NOT NULL UNIQUE,
  user_uuid VARCHAR(255) NOT NULL,
  task_type VARCHAR(50) NOT NULL,
  provider VARCHAR(50) NOT NULL,
  provider_task_id VARCHAR(255),
  status VARCHAR(50) NOT NULL DEFAULT 'pending',
  progress DECIMAL(5,2) DEFAULT 0.00,
  input_params JSONB NOT NULL,
  result_urls JSONB,
  result_metadata JSONB,
  error_code VARCHAR(50),
  error_message TEXT,
  credits_cost INTEGER DEFAULT 0,
  credits_transaction_id VARCHAR(255),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  started_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  CONSTRAINT fk_ai_tasks_user FOREIGN KEY (user_uuid) REFERENCES users(uuid),
  CONSTRAINT fk_ai_tasks_credits FOREIGN KEY (credits_transaction_id) REFERENCES credits(trans_no)
);

CREATE INDEX idx_ai_tasks_user_uuid ON ai_tasks(user_uuid);
CREATE INDEX idx_ai_tasks_status ON ai_tasks(status);
CREATE INDEX idx_ai_tasks_provider_task_id ON ai_tasks(provider, provider_task_id);
CREATE INDEX idx_ai_tasks_created_at ON ai_tasks(created_at);
```

## 🔧 模型层实现

### 1. AI 任务模型

```typescript
// src/models/ai-task.ts

import { aiTasks } from "@/db/schema";
import { db } from "@/db";
import { eq, and, desc } from "drizzle-orm";
import { getUuid } from "@/lib/hash";

export enum TaskStatus {
  Pending = "pending",
  Processing = "processing", 
  Completed = "completed",
  Failed = "failed",
  Cancelled = "cancelled",
}

export enum TaskType {
  PetPortrait = "pet_portrait",
  ImageGeneration = "image_generation",
}

export enum AIProvider {
  ApiCore = "apicore",
  Replicate = "replicate",
  KIE = "kie",
}

export interface CreateTaskParams {
  user_uuid: string;
  task_type: TaskType;
  provider: AIProvider;
  input_params: Record<string, any>;
  credits_cost: number;
}

export interface UpdateTaskParams {
  provider_task_id?: string;
  status?: TaskStatus;
  progress?: number;
  result_urls?: string[];
  result_metadata?: Record<string, any>;
  error_code?: string;
  error_message?: string;
  started_at?: Date;
  completed_at?: Date;
}

export class AITaskModel {
  // 创建新任务
  static async create(params: CreateTaskParams) {
    const taskUuid = getUuid();
    
    const [task] = await db().insert(aiTasks).values({
      uuid: taskUuid,
      user_uuid: params.user_uuid,
      task_type: params.task_type,
      provider: params.provider,
      input_params: params.input_params,
      credits_cost: params.credits_cost,
      status: TaskStatus.Pending,
    }).returning();

    return task;
  }

  // 根据 UUID 查找任务
  static async findByUuid(uuid: string) {
    const [task] = await db()
      .select()
      .from(aiTasks)
      .where(eq(aiTasks.uuid, uuid));
    
    return task || null;
  }

  // 根据用户查找任务列表
  static async findByUser(user_uuid: string, limit = 20, offset = 0) {
    return await db()
      .select()
      .from(aiTasks)
      .where(eq(aiTasks.user_uuid, user_uuid))
      .orderBy(desc(aiTasks.created_at))
      .limit(limit)
      .offset(offset);
  }

  // 根据提供商任务ID查找
  static async findByProviderTaskId(provider: AIProvider, provider_task_id: string) {
    const [task] = await db()
      .select()
      .from(aiTasks)
      .where(
        and(
          eq(aiTasks.provider, provider),
          eq(aiTasks.provider_task_id, provider_task_id)
        )
      );
    
    return task || null;
  }

  // 更新任务
  static async update(uuid: string, params: UpdateTaskParams) {
    const updateData: any = { ...params };
    
    // 处理时间戳
    if (params.started_at) updateData.started_at = params.started_at;
    if (params.completed_at) updateData.completed_at = params.completed_at;
    
    const [task] = await db()
      .update(aiTasks)
      .set(updateData)
      .where(eq(aiTasks.uuid, uuid))
      .returning();

    return task;
  }

  // 删除任务（软删除，更新状态为 cancelled）
  static async cancel(uuid: string) {
    return await this.update(uuid, {
      status: TaskStatus.Cancelled,
      completed_at: new Date(),
    });
  }
}
```

### 2. 积分集成

```typescript
// src/models/credits-integration.ts

import { credits } from "@/db/schema";
import { db } from "@/db";
import { getUuid } from "@/lib/hash";

export enum CreditTransactionType {
  AITaskCost = "ai_task_cost",
  AITaskRefund = "ai_task_refund",
}

export class CreditsIntegration {
  // 预扣积分
  static async reserveCredits(
    user_uuid: string, 
    amount: number, 
    task_uuid: string
  ) {
    const transNo = getUuid();
    
    const [transaction] = await db().insert(credits).values({
      trans_no: transNo,
      user_uuid,
      trans_type: CreditTransactionType.AITaskCost,
      credits: -amount, // 负数表示消费
      order_no: task_uuid, // 使用任务UUID作为订单号
      created_at: new Date(),
    }).returning();

    return transaction;
  }

  // 退还积分
  static async refundCredits(
    user_uuid: string,
    amount: number,
    original_trans_no: string,
    task_uuid: string
  ) {
    const transNo = getUuid();
    
    const [transaction] = await db().insert(credits).values({
      trans_no: transNo,
      user_uuid,
      trans_type: CreditTransactionType.AITaskRefund,
      credits: amount, // 正数表示退款
      order_no: task_uuid,
      created_at: new Date(),
    }).returning();

    return transaction;
  }

  // 检查用户积分余额
  static async getUserBalance(user_uuid: string): Promise<number> {
    const result = await db()
      .select({
        total: sql<number>`COALESCE(SUM(credits), 0)`
      })
      .from(credits)
      .where(eq(credits.user_uuid, user_uuid));

    return result[0]?.total || 0;
  }
}
```

## 🛣️ API 路由实现

### 1. 任务管理 API

```typescript
// src/app/api/ai/tasks/route.ts

import { NextRequest } from "next/server";
import { respData, respErr } from "@/lib/resp";
import { AITaskModel, TaskType, AIProvider } from "@/models/ai-task";
import { CreditsIntegration } from "@/models/credits-integration";
import { getUserFromRequest } from "@/lib/auth";

// 创建任务
export async function POST(req: NextRequest) {
  try {
    const user = await getUserFromRequest(req);
    if (!user) {
      return respErr("Unauthorized", 401);
    }

    const body = await req.json();
    const { task_type, provider, input_params, credits_cost } = body;

    // 验证参数
    if (!task_type || !provider || !input_params) {
      return respErr("Missing required parameters");
    }

    // 检查积分余额
    const balance = await CreditsIntegration.getUserBalance(user.uuid);
    if (balance < credits_cost) {
      return respErr("Insufficient credits");
    }

    // 预扣积分
    const creditTransaction = await CreditsIntegration.reserveCredits(
      user.uuid,
      credits_cost,
      "" // 任务UUID稍后更新
    );

    // 创建任务
    const task = await AITaskModel.create({
      user_uuid: user.uuid,
      task_type: task_type as TaskType,
      provider: provider as AIProvider,
      input_params,
      credits_cost,
    });

    // 更新积分交易记录
    await AITaskModel.update(task.uuid, {
      credits_transaction_id: creditTransaction.trans_no,
    });

    return respData({
      task_uuid: task.uuid,
      status: task.status,
      created_at: task.created_at,
    });

  } catch (error) {
    console.error("Create AI task failed:", error);
    return respErr("Failed to create task");
  }
}

// 获取任务列表
export async function GET(req: NextRequest) {
  try {
    const user = await getUserFromRequest(req);
    if (!user) {
      return respErr("Unauthorized", 401);
    }

    const { searchParams } = new URL(req.url);
    const limit = parseInt(searchParams.get("limit") || "20");
    const offset = parseInt(searchParams.get("offset") || "0");

    const tasks = await AITaskModel.findByUser(user.uuid, limit, offset);

    return respData({
      tasks,
      pagination: {
        limit,
        offset,
        total: tasks.length,
      },
    });

  } catch (error) {
    console.error("Get AI tasks failed:", error);
    return respErr("Failed to get tasks");
  }
}
```

### 2. 单个任务 API

```typescript
// src/app/api/ai/tasks/[uuid]/route.ts

import { NextRequest } from "next/server";
import { respData, respErr } from "@/lib/resp";
import { AITaskModel } from "@/models/ai-task";
import { getUserFromRequest } from "@/lib/auth";

// 获取任务详情
export async function GET(
  req: NextRequest,
  { params }: { params: { uuid: string } }
) {
  try {
    const user = await getUserFromRequest(req);
    if (!user) {
      return respErr("Unauthorized", 401);
    }

    const task = await AITaskModel.findByUuid(params.uuid);
    if (!task) {
      return respErr("Task not found", 404);
    }

    // 检查权限
    if (task.user_uuid !== user.uuid) {
      return respErr("Access denied", 403);
    }

    return respData(task);

  } catch (error) {
    console.error("Get AI task failed:", error);
    return respErr("Failed to get task");
  }
}

// 取消任务
export async function DELETE(
  req: NextRequest,
  { params }: { params: { uuid: string } }
) {
  try {
    const user = await getUserFromRequest(req);
    if (!user) {
      return respErr("Unauthorized", 401);
    }

    const task = await AITaskModel.findByUuid(params.uuid);
    if (!task) {
      return respErr("Task not found", 404);
    }

    // 检查权限
    if (task.user_uuid !== user.uuid) {
      return respErr("Access denied", 403);
    }

    // 只能取消 pending 或 processing 状态的任务
    if (!['pending', 'processing'].includes(task.status)) {
      return respErr("Cannot cancel completed task");
    }

    // 取消任务
    const updatedTask = await AITaskModel.cancel(params.uuid);

    // 退还积分
    if (task.credits_cost > 0) {
      await CreditsIntegration.refundCredits(
        user.uuid,
        task.credits_cost,
        task.credits_transaction_id || "",
        task.uuid
      );
    }

    return respData(updatedTask);

  } catch (error) {
    console.error("Cancel AI task failed:", error);
    return respErr("Failed to cancel task");
  }
}
```

## 🔗 Webhook 处理

### 1. 通用 Webhook 处理器

```typescript
// src/app/api/webhooks/ai/[provider]/route.ts

import { NextRequest } from "next/server";
import { respData, respErr } from "@/lib/resp";
import { AITaskModel, TaskStatus, AIProvider } from "@/models/ai-task";
import { CreditsIntegration } from "@/models/credits-integration";
import { verifyWebhookSignature } from "@/lib/webhook-security";

export async function POST(
  req: NextRequest,
  { params }: { params: { provider: string } }
) {
  try {
    const provider = params.provider as AIProvider;
    const body = await req.text();
    const signature = req.headers.get("x-signature") || "";

    // 验证签名
    const secret = process.env[`WEBHOOK_SECRET_${provider.toUpperCase()}`];
    if (secret && !verifyWebhookSignature(body, signature, secret)) {
      return respErr("Invalid signature", 401);
    }

    const data = JSON.parse(body);
    
    // 根据不同提供商处理数据
    const result = await processWebhookData(provider, data);
    
    return respData(result);

  } catch (error) {
    console.error(`Webhook processing failed for ${params.provider}:`, error);
    return respErr("Webhook processing failed");
  }
}

async function processWebhookData(provider: AIProvider, data: any) {
  switch (provider) {
    case AIProvider.KIE:
      return await processKIEWebhook(data);
    case AIProvider.Replicate:
      return await processReplicateWebhook(data);
    case AIProvider.ApiCore:
      return await processApiCoreWebhook(data);
    default:
      throw new Error(`Unsupported provider: ${provider}`);
  }
}

async function processKIEWebhook(data: any) {
  const { taskId, successFlag, response, errorMessage } = data;
  
  // 查找对应的任务
  const task = await AITaskModel.findByProviderTaskId(AIProvider.KIE, taskId);
  if (!task) {
    throw new Error(`Task not found for KIE taskId: ${taskId}`);
  }

  const updateParams: any = {};

  if (successFlag === 1) {
    // 任务成功
    updateParams.status = TaskStatus.Completed;
    updateParams.progress = 100;
    updateParams.result_urls = response?.resultUrls || [];
    updateParams.result_metadata = response;
    updateParams.completed_at = new Date();
  } else if (successFlag === 2) {
    // 任务失败
    updateParams.status = TaskStatus.Failed;
    updateParams.error_message = errorMessage;
    updateParams.completed_at = new Date();

    // 退还积分
    if (task.credits_cost > 0) {
      await CreditsIntegration.refundCredits(
        task.user_uuid,
        task.credits_cost,
        task.credits_transaction_id || "",
        task.uuid
      );
    }
  }

  // 更新任务状态
  const updatedTask = await AITaskModel.update(task.uuid, updateParams);
  
  return { task_uuid: task.uuid, status: updatedTask.status };
}
```

## 🎨 前端组件实现

### 1. 任务提交组件

```typescript
// src/components/ai/TaskSubmissionForm.tsx

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";

interface TaskSubmissionFormProps {
  taskType: string;
  onTaskCreated: (taskUuid: string) => void;
}

export function TaskSubmissionForm({ taskType, onTaskCreated }: TaskSubmissionFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async (formData: FormData) => {
    setIsSubmitting(true);
    
    try {
      const response = await fetch("/api/ai/tasks", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          task_type: taskType,
          provider: formData.get("provider"),
          input_params: {
            prompt: formData.get("prompt"),
            style: formData.get("style"),
            // ... 其他参数
          },
          credits_cost: 10, // 根据实际情况计算
        }),
      });

      const result = await response.json();
      
      if (result.success) {
        toast({
          title: "任务已提交",
          description: "正在处理中，请稍候...",
        });
        onTaskCreated(result.data.task_uuid);
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      toast({
        title: "提交失败",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form action={handleSubmit}>
      {/* 表单字段 */}
      <Button type="submit" disabled={isSubmitting}>
        {isSubmitting ? "提交中..." : "开始生成"}
      </Button>
    </form>
  );
}
```

### 2. 任务进度组件

```typescript
// src/components/ai/TaskProgressCard.tsx

import { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";

interface TaskProgressCardProps {
  taskUuid: string;
  onCompleted: (task: any) => void;
}

export function TaskProgressCard({ taskUuid, onCompleted }: TaskProgressCardProps) {
  const [task, setTask] = useState<any>(null);
  const [isPolling, setIsPolling] = useState(true);

  useEffect(() => {
    if (!isPolling) return;

    const pollTask = async () => {
      try {
        const response = await fetch(`/api/ai/tasks/${taskUuid}`);
        const result = await response.json();
        
        if (result.success) {
          setTask(result.data);
          
          if (['completed', 'failed', 'cancelled'].includes(result.data.status)) {
            setIsPolling(false);
            if (result.data.status === 'completed') {
              onCompleted(result.data);
            }
          }
        }
      } catch (error) {
        console.error("Poll task failed:", error);
      }
    };

    // 立即执行一次
    pollTask();

    // 设置轮询
    const interval = setInterval(pollTask, 10000); // 10秒轮询一次

    return () => clearInterval(interval);
  }, [taskUuid, isPolling, onCompleted]);

  if (!task) {
    return <div>加载中...</div>;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          任务进度
          <Badge variant={getStatusVariant(task.status)}>
            {getStatusText(task.status)}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Progress value={task.progress || 0} className="mb-4" />
        <p className="text-sm text-muted-foreground">
          {task.progress || 0}% 完成
        </p>
        {task.error_message && (
          <p className="text-sm text-red-500 mt-2">
            错误: {task.error_message}
          </p>
        )}
      </CardContent>
    </Card>
  );
}

function getStatusVariant(status: string) {
  switch (status) {
    case 'completed': return 'success';
    case 'failed': return 'destructive';
    case 'cancelled': return 'secondary';
    default: return 'default';
  }
}

function getStatusText(status: string) {
  switch (status) {
    case 'pending': return '等待中';
    case 'processing': return '处理中';
    case 'completed': return '已完成';
    case 'failed': return '失败';
    case 'cancelled': return '已取消';
    default: return status;
  }
}
```

---

*本技术实现指南将随着开发进展持续更新和完善*
