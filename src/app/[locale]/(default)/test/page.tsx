'use client';

import { useState, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Textarea } from '@/components/ui/textarea';

export default function TestPage() {
  const [file, setFile] = useState<File | null>(null);
  const [preview, setPreview] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string>('');
  
  // 参数状态
  const [petType, setPetType] = useState('cat');
  const [style, setStyle] = useState('realistic portrait');
  const [description, setDescription] = useState('');
  const [model, setModel] = useState('gpt-4o-image');
  const [size, setSize] = useState('1024x1024');
  const [provider, setProvider] = useState('apicore');
  
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (selectedFile: File) => {
    if (selectedFile && selectedFile.type.startsWith('image/')) {
      setFile(selectedFile);
      setError('');
      
      // 创建预览
      const reader = new FileReader();
      reader.onload = (e) => {
        setPreview(e.target?.result as string);
      };
      reader.readAsDataURL(selectedFile);
    } else {
      setError('请选择有效的图片文件');
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    const droppedFile = e.dataTransfer.files[0];
    if (droppedFile) {
      handleFileSelect(droppedFile);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      handleFileSelect(selectedFile);
    }
  };

  const handleSubmit = async () => {
    if (!file || !petType) {
      setError('请选择图片文件和宠物类型');
      return;
    }

    setLoading(true);
    setError('');
    setResult(null);

    try {
      const formData = new FormData();
      formData.append('image', file);
      formData.append('petType', petType);
      formData.append('style', style);
      formData.append('description', description);
      formData.append('model', model);
      formData.append('size', size);
      formData.append('provider', provider);

      const response = await fetch('/api/pet-portrait/generate', {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();
      
      if (response.ok) {
        setResult(data);
        setError('');
      } else {
        setError(data.message || 'API调用失败');
      }
    } catch (err) {
      setError('请求失败: ' + (err instanceof Error ? err.message : String(err)));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">宠物肖像 API 测试</h1>
        <p className="text-gray-600">测试 ApiCore 图像生成接口</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 左侧：参数配置 */}
        <Card>
          <CardHeader>
            <CardTitle>配置参数</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* 文件上传 */}
            <div>
              <Label htmlFor="file">上传宠物图片</Label>
              <div
                className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer hover:border-gray-400 transition-colors"
                onDrop={handleDrop}
                onDragOver={handleDragOver}
                onClick={() => fileInputRef.current?.click()}
              >
                {preview ? (
                  <div>
                    <img src={preview} alt="预览" className="max-w-full h-32 object-contain mx-auto mb-2" />
                    <p className="text-sm text-gray-600">{file?.name}</p>
                  </div>
                ) : (
                  <div>
                    <p className="text-gray-500">点击或拖拽图片到这里</p>
                    <p className="text-sm text-gray-400">支持 JPG, PNG 格式</p>
                  </div>
                )}
              </div>
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleFileInputChange}
                className="hidden"
              />
            </div>

            {/* 宠物类型 */}
            <div>
              <Label htmlFor="petType">宠物类型</Label>
              <Select value={petType} onValueChange={setPetType}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="cat">猫</SelectItem>
                  <SelectItem value="dog">狗</SelectItem>
                  <SelectItem value="rabbit">兔子</SelectItem>
                  <SelectItem value="hamster">仓鼠</SelectItem>
                  <SelectItem value="bird">鸟</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* 风格选择 */}
            <div>
              <Label htmlFor="style">艺术风格</Label>
              <Select value={style} onValueChange={setStyle}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="realistic portrait">写实肖像</SelectItem>
                  <SelectItem value="oil painting">油画风格</SelectItem>
                  <SelectItem value="watercolor">水彩画</SelectItem>
                  <SelectItem value="pixel art">像素艺术</SelectItem>
                  <SelectItem value="cartoon style">卡通风格</SelectItem>
                  <SelectItem value="pencil sketch">铅笔素描</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* 描述 */}
            <div>
              <Label htmlFor="description">额外描述 (可选)</Label>
              <Textarea
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="例如：戴着帽子、在花园里..."
                rows={3}
              />
            </div>

            {/* Provider 选择 */}
            <div>
              <Label htmlFor="provider">AI 提供商</Label>
              <Select value={provider} onValueChange={setProvider}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="apicore">ApiCore</SelectItem>
                  <SelectItem value="replicate">Replicate</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* 模型选择 */}
            <div>
              <Label htmlFor="model">模型</Label>
              <Select value={model} onValueChange={setModel}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {provider === 'apicore' ? (
                    <>
                      <SelectItem value="gpt-4o-image">GPT-4O Image</SelectItem>
                      <SelectItem value="dall-e-3">DALL-E 3</SelectItem>
                      <SelectItem value="stable-diffusion-xl">Stable Diffusion XL</SelectItem>
                    </>
                  ) : (
                    <>
                      <SelectItem value="stable-diffusion-xl">Stable Diffusion XL</SelectItem>
                      <SelectItem value="gpt-4o-image">SDXL (映射)</SelectItem>
                      <SelectItem value="dall-e-3">SDXL (映射)</SelectItem>
                    </>
                  )}
                </SelectContent>
              </Select>
            </div>

            {/* 尺寸选择 */}
            <div>
              <Label htmlFor="size">图片尺寸</Label>
              <Select value={size} onValueChange={setSize}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="512x512">512x512</SelectItem>
                  <SelectItem value="1024x1024">1024x1024</SelectItem>
                  <SelectItem value="1024x1792">1024x1792</SelectItem>
                  <SelectItem value="1792x1024">1792x1024</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* 提交按钮 */}
            <Button 
              onClick={handleSubmit} 
              disabled={!file || !petType || loading}
              className="w-full"
            >
              {loading ? '生成中...' : '生成肖像'}
            </Button>
          </CardContent>
        </Card>

        {/* 右侧：结果展示 */}
        <Card>
          <CardHeader>
            <CardTitle>生成结果</CardTitle>
          </CardHeader>
          <CardContent>
            {error && (
              <Alert className="mb-4">
                <AlertDescription className="text-red-600">
                  {error}
                </AlertDescription>
              </Alert>
            )}

            {loading && (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <span className="ml-2">正在生成...</span>
              </div>
            )}

            {result && (
              <div className="space-y-4">
                <div>
                  <h3 className="font-semibold mb-2">生成的肖像:</h3>
                  {result.data?.imageUrl ? (
                    <div className="relative">
                      <img
                        src={result.data.imageUrl}
                        alt="生成的宠物肖像"
                        className="max-w-full rounded-lg shadow-lg"
                        onLoad={() => console.log('图片加载成功')}
                        onError={(e) => {
                          console.error('图片加载失败:', e);
                          setError('图片加载失败，请检查网络连接');
                        }}
                      />
                      <div className="mt-2 text-sm text-gray-600">
                        <a
                          href={result.data.imageUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:underline"
                        >
                          在新窗口中打开图片
                        </a>
                      </div>
                    </div>
                  ) : result.data?.base64 ? (
                    <img
                      src={`data:image/png;base64,${result.data.base64}`}
                      alt="生成的宠物肖像"
                      className="max-w-full rounded-lg shadow-lg"
                      onLoad={() => console.log('Base64图片加载成功')}
                      onError={(e) => {
                        console.error('Base64图片加载失败:', e);
                        setError('图片数据格式错误');
                      }}
                    />
                  ) : result.imageUrl ? (
                    // 兼容旧格式
                    <div className="relative">
                      <img
                        src={result.imageUrl}
                        alt="生成的宠物肖像"
                        className="max-w-full rounded-lg shadow-lg"
                        onLoad={() => console.log('图片加载成功')}
                        onError={(e) => {
                          console.error('图片加载失败:', e);
                          setError('图片加载失败，请检查网络连接');
                        }}
                      />
                      <div className="mt-2 text-sm text-gray-600">
                        <a
                          href={result.imageUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:underline"
                        >
                          在新窗口中打开图片
                        </a>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <p>未找到图片数据</p>
                      <p className="text-sm mt-2">请检查 API 响应格式</p>
                    </div>
                  )}
                </div>

                <div className="text-sm space-y-1">
                  <p><strong>AI 提供商:</strong> {result.data?.provider || provider}</p>
                  <p><strong>使用模型:</strong> {result.data?.model || result.model}</p>
                  <p><strong>提示词:</strong> {result.data?.prompt || result.prompt}</p>
                  <p><strong>宠物类型:</strong> {result.data?.petType || result.petType}</p>
                  <p><strong>艺术风格:</strong> {result.data?.style || result.style}</p>
                  {result.data?.size && <p><strong>图片尺寸:</strong> {result.data.size}</p>}
                  {result.data?.count && <p><strong>生成数量:</strong> {result.data.count}</p>}
                  {result.cost && <p><strong>成本:</strong> {result.cost}分</p>}
                </div>

                <div className="bg-gray-50 p-3 rounded-lg">
                  <h4 className="font-semibold mb-2">完整响应:</h4>
                  <pre className="text-xs overflow-auto">
                    {JSON.stringify(result, null, 2)}
                  </pre>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}