import { experimental_generateImage as generateImage, JSONValue } from "ai";
import type { ImageModelV1 } from "@ai-sdk/provider";
import { apicore } from "@/aisdk/apicore";
import { replicate } from "@ai-sdk/replicate";
import { kie } from "@/aisdk/kie";
import { respData, respErr } from "@/lib/resp";
import { newStorage } from "@/lib/storage";
import { getUuid } from "@/lib/hash";

import { safeBase64Decode } from "@/lib/base64-utils";

export async function POST(req: Request) {
  console.log("=== Pet Portrait API Start ===");
  const startTime = Date.now();
  
  // 环境检测
  console.log("=== Environment Detection ===");
  console.log("- typeof Buffer:", typeof Buffer);
  console.log("- typeof atob:", typeof atob);
  console.log("- typeof FormData:", typeof FormData);
  console.log("- typeof Blob:", typeof Blob);
  console.log("- globalThis.navigator:", typeof globalThis.navigator);
  // @ts-ignore
  console.log("- CF Workers check:", typeof globalThis.caches !== 'undefined' ? "Yes" : "No");

  try {
    // 支持FormData (图像上传) 和 JSON 两种格式
    let petType, style, description, model, size, petImage, provider;
    let formData: FormData | null = null;

    const contentType = req.headers.get("content-type");

    if (contentType?.includes("multipart/form-data")) {
      // 处理FormData格式
      formData = await req.formData();
      petType = formData.get("petType") as string;
      style = (formData.get("style") as string) || "realistic portrait";
      description = (formData.get("description") as string) || "";
      model = (formData.get("model") as string) || "gpt-4o-image";
      size = (formData.get("size") as string) || "1024x1024";
      provider = (formData.get("provider") as string) || "apicore";
      petImage = formData.get("image") as File;
    } else {
      // 处理JSON格式 (base64图像)
      const body = await req.json();
      petType = body.petType;
      style = body.style || "realistic portrait";
      description = body.description || "";
      model = body.model || "gpt-4o-image";
      size = body.size || "1024x1024";
      provider = body.provider || "apicore";
      petImage = body.image; // base64 string
    }

    if (!petType || !petImage) {
      return respErr("Pet type and image are required");
    }

    // 构建宠物肖像专用prompt
    const prompt = `Transform this ${petType} photo into a beautiful ${style}. ${description}. High quality, detailed, professional artistic portrait, studio lighting, sharp focus, artistic composition.`;

    console.log(`使用 provider: ${provider}, model: ${model}`);

    // 根据 provider 选择不同的图像模型和配置
    let imageModel: ImageModelV1;
    let providerOptions: Record<string, Record<string, JSONValue>> = {};

    switch (provider) {
      case "apicore":
        imageModel = apicore.image(model);
        providerOptions = {
          apicore: {
            image: petImage, // 用户上传的宠物图片
            response_format: "url",
            size,
          },
        };
        break;

      case "replicate":
        // 直接使用传入的模型名称，不做映射
        imageModel = replicate.image(model);

        // Replicate 需要不同的参数格式
        providerOptions = {
          replicate: {
            // Replicate 通常需要 prompt 和 image 作为输入
            prompt: prompt,
            image: petImage, // 可能需要转换为 URL
            width: parseInt(size.split('x')[0]) || 1024,
            height: parseInt(size.split('x')[1]) || 1024,
            num_outputs: 1,
            scheduler: "K_EULER",
            num_inference_steps: 50,
            guidance_scale: 7.5,
          },
        };
        break;

      case "kie":
        // 直接使用传入的模型名称
        imageModel = kie.image(model as any);

        // KIE 需要图片 URL，需要先上传图片
        let petImageUrl: string | undefined;
        if (petImage) {
          // 如果是 File 对象，需要先上传到存储获取 URL
          if (petImage instanceof File || petImage instanceof Blob) {
            const storage = newStorage();
            const tempKey = `temp/${getUuid()}.${petImage instanceof File ? petImage.name.split('.').pop() : 'png'}`;

            const imageBytes = petImage instanceof File
              ? new Uint8Array(await petImage.arrayBuffer())
              : new Uint8Array(await petImage.arrayBuffer());

            const uploadResult = await storage.uploadFile({
              body: imageBytes,
              key: tempKey,
              contentType: petImage instanceof File ? petImage.type : "image/png",
              disposition: "inline",
            });

            petImageUrl = uploadResult.url;
            console.log("Uploaded pet image for KIE:", petImageUrl);
          } else if (typeof petImage === 'string') {
            petImageUrl = petImage;
          }
        }

        const kieOptions: Record<string, JSONValue> = {
          nVariants: 1,
          isEnhance: true,
          enableFallback: true,
          fallbackModel: "FLUX_MAX",
        };

        if (petImageUrl) {
          kieOptions.filesUrl = [petImageUrl];
        }

        // 检查是否启用 webhook 模式
        const useWebhook = process.env.NEXT_PUBLIC_WEB_URL && formData && formData.get("use_webhook") !== "false";
        if (useWebhook) {
          console.log("启用 KIE Webhook 异步模式");
          kieOptions.mode = 'webhook';
          kieOptions.callBackUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/api/webhooks/kie`;
          kieOptions.task_input_data = {
            petType,
            style,
            description,
            model,
            size,
            provider,
          };
        }

        providerOptions = {
          kie: kieOptions,
        };
        break;

      default:
        return respErr(`不支持的 provider: ${provider}`);
    }

    let images, warnings;
    try {
      console.log(`开始调用 ${provider} 图像生成...`);
      const result = await generateImage({
        model: imageModel,
        prompt,
        n: 1,
        size, // 传递 size 参数给 AI SDK
        providerOptions,
      });
      images = result.images;
      warnings = result.warnings;

      // 检查是否是异步任务
      const isAsyncTask = warnings?.some((w: any) => w.type === 'async-task-created');
      if (isAsyncTask && provider === 'kie') {
        console.log(`KIE Webhook 模式，立即返回任务ID`);
        const taskInfo = (result as any).response;
        return respData({
          task_id: taskInfo.taskId,
          provider_task_id: taskInfo.providerTaskId,
          status: 'pending',
          mode: 'webhook',
          message: '任务已提交，正在处理中...',
        });
      }

      console.log(`${provider} 图像生成完成`);
    } catch (genError) {
      console.error(`=== ${provider} generateImage Error ===`);
      console.error("Error name:", genError?.constructor?.name);
      console.error("Error message:", genError instanceof Error ? genError.message : String(genError));
      console.error("Error stack:", genError instanceof Error ? genError.stack : "No stack");
      if (genError instanceof Error && 'cause' in genError) {
        console.error("Error cause:", genError.cause);
      }
      throw genError;
    }

    if (warnings.length > 0) {
      console.log("Pet portrait generation warnings:", warnings);
      return respErr("Pet portrait generation failed");
    }

    // 上传到存储
    const storage = newStorage();
    const batch = getUuid();
    const filename = `${batch}.png`;
    const key = `pet-portrait/${filename}`;

    try {
      // CF Workers兼容的base64解码
      const imageBytes = safeBase64Decode(images[0].base64);
      
      const uploadResult = await storage.uploadFile({
        body: imageBytes,
        key,
        contentType: "image/png",
        disposition: "inline",
      });

      console.log("uploadResult", uploadResult);

      return respData({
        imageUrl: uploadResult.url,
        filename,
        prompt,
        model,
        provider,
        petType,
        style,
        size,
        count: images.length,
      });
    } catch (err) {
      console.log("Upload file failed:", err);
      return respData({
        base64: images[0].base64,
        prompt,
        model,
        provider,
        petType,
        style,
        size,
        filename,
      });
    }
  } catch (err) {
    console.error("=== MAIN ERROR CAUGHT ===");
    console.error("Error type:", err?.constructor?.name);
    console.error("Error message:", err instanceof Error ? err.message : String(err));
    console.error("Error stack:", err instanceof Error ? err.stack : "No stack");
    
    // 详细错误分析
    if (err instanceof Error) {
      console.error("Error details:");
      Object.keys(err).forEach(key => {
        console.error(`- ${key}:`, (err as any)[key]);
      });
      
      if ('cause' in err) {
        console.error("Error cause:", err.cause);
        if (err.cause && typeof err.cause === 'object') {
          Object.keys(err.cause).forEach(key => {
            console.error(`  - cause.${key}:`, (err.cause as any)[key]);
          });
        }
      }
    }
    
    console.error("Request took:", Date.now() - startTime, "ms");
    
    return respErr("Failed to generate pet portrait: " + (err instanceof Error ? err.message : String(err)));
  }
}