import { petPortraitTasks } from "@/db/schema";
import { db } from "@/db";
import { eq, and, desc } from "drizzle-orm";
import { getUuid } from "@/lib/hash";

export enum TaskStatus {
  Pending = "pending",
  Completed = "completed",
  Failed = "failed",
}

export enum TaskMode {
  Webhook = "webhook",
  Polling = "polling",
}

export enum AIProvider {
  ApiCore = "apicore",
  Replicate = "replicate",
  KIE = "kie",
}

export interface PetPortraitTask {
  id: number;
  task_id: string;
  provider_task_id?: string;
  provider: string;
  mode: string;
  status: string;
  input_data: string; // JSON string
  result_urls?: string; // JSON string
  error_message?: string;
  created_at: Date;
  completed_at?: Date;
}

export interface CreateTaskParams {
  provider: AIProvider;
  mode?: TaskMode;
  input_data: Record<string, any>;
}

export interface UpdateTaskParams {
  provider_task_id?: string;
  status?: TaskStatus;
  result_urls?: string[];
  error_message?: string;
  completed_at?: Date;
}

export class PetPortraitTaskModel {
  /**
   * 创建新任务
   */
  static async create(params: CreateTaskParams): Promise<PetPortraitTask> {
    const taskId = getUuid();
    
    const [task] = await db().insert(petPortraitTasks).values({
      task_id: taskId,
      provider: params.provider,
      mode: params.mode || TaskMode.Webhook,
      status: TaskStatus.Pending,
      input_data: typeof params.input_data === 'string'
        ? params.input_data
        : JSON.stringify(params.input_data || {}),
    }).returning();

    return task as PetPortraitTask;
  }

  /**
   * 根据 task_id 查找任务
   */
  static async findByTaskId(task_id: string): Promise<PetPortraitTask | null> {
    const [task] = await db()
      .select()
      .from(petPortraitTasks)
      .where(eq(petPortraitTasks.task_id, task_id));
    
    return task ? (task as PetPortraitTask) : null;
  }

  /**
   * 根据 provider 和 provider_task_id 查找任务
   */
  static async findByProviderTaskId(
    provider: AIProvider, 
    provider_task_id: string
  ): Promise<PetPortraitTask | null> {
    const [task] = await db()
      .select()
      .from(petPortraitTasks)
      .where(
        and(
          eq(petPortraitTasks.provider, provider),
          eq(petPortraitTasks.provider_task_id, provider_task_id)
        )
      );
    
    return task ? (task as PetPortraitTask) : null;
  }

  /**
   * 获取指定状态和模式的任务列表（用于轮询处理）
   */
  static async findByStatusAndMode(
    status: TaskStatus,
    mode: TaskMode,
    limit = 50
  ): Promise<PetPortraitTask[]> {
    const tasks = await db()
      .select()
      .from(petPortraitTasks)
      .where(
        and(
          eq(petPortraitTasks.status, status),
          eq(petPortraitTasks.mode, mode)
        )
      )
      .orderBy(desc(petPortraitTasks.created_at))
      .limit(limit);

    return tasks as PetPortraitTask[];
  }

  /**
   * 更新任务
   */
  static async update(task_id: string, params: UpdateTaskParams): Promise<PetPortraitTask | null> {
    const updateData: any = {};
    
    if (params.provider_task_id !== undefined) {
      updateData.provider_task_id = params.provider_task_id;
    }
    if (params.status !== undefined) {
      updateData.status = params.status;
    }
    if (params.result_urls !== undefined) {
      // 确保 result_urls 能正确序列化
      try {
        if (typeof params.result_urls === 'string') {
          updateData.result_urls = params.result_urls;
        } else if (Array.isArray(params.result_urls)) {
          updateData.result_urls = JSON.stringify(params.result_urls);
        } else {
          console.error('Invalid result_urls type:', typeof params.result_urls, params.result_urls);
          updateData.result_urls = JSON.stringify([]);
        }
      } catch (error) {
        console.error('Failed to serialize result_urls:', error, params.result_urls);
        updateData.result_urls = JSON.stringify([]);
      }
    }
    if (params.error_message !== undefined) {
      updateData.error_message = params.error_message;
    }
    if (params.completed_at !== undefined) {
      updateData.completed_at = params.completed_at;
    }

    const [task] = await db()
      .update(petPortraitTasks)
      .set(updateData)
      .where(eq(petPortraitTasks.task_id, task_id))
      .returning();

    return task ? (task as PetPortraitTask) : null;
  }

  /**
   * 解析 input_data JSON 字符串
   */
  static parseInputData(task: PetPortraitTask): Record<string, any> {
    if (!task.input_data) {
      return {};
    }

    try {
      return JSON.parse(task.input_data);
    } catch (error) {
      console.error("Failed to parse input_data:", error);
      console.error("Raw input_data:", task.input_data);

      // 尝试使用 eval 解析对象字面量（仅用于修复现有数据）
      try {
        // 安全检查：确保只包含对象字面量
        if (task.input_data.trim().startsWith('{') && task.input_data.trim().endsWith('}')) {
          console.log("Attempting to parse as object literal");
          const result = eval(`(${task.input_data})`);
          if (typeof result === 'object' && result !== null) {
            console.log("Successfully parsed as object literal");
            return result;
          }
        }
      } catch (evalError) {
        console.error("Failed to parse as object literal:", evalError);
      }

      return {};
    }
  }

  /**
   * 解析 result_urls JSON 字符串
   */
  static parseResultUrls(task: PetPortraitTask): string[] {
    if (!task.result_urls) return [];

    try {
      const parsed = JSON.parse(task.result_urls);
      if (Array.isArray(parsed)) {
        return parsed;
      } else {
        console.error("result_urls is not an array:", parsed);
        return [];
      }
    } catch (error) {
      console.error("Failed to parse result_urls:", error);
      console.error("Raw result_urls:", task.result_urls);

      // 尝试解析数组字面量（修复现有数据）
      try {
        if (task.result_urls.trim().startsWith('[') && task.result_urls.trim().endsWith(']')) {
          console.log("Attempting to parse as array literal");
          const result = eval(`(${task.result_urls})`);
          if (Array.isArray(result)) {
            console.log("Successfully parsed as array literal");
            return result;
          }
        }
      } catch (evalError) {
        console.error("Failed to parse as array literal:", evalError);
      }

      // 尝试作为单个 URL 处理
      if (typeof task.result_urls === 'string' && task.result_urls.startsWith('http')) {
        console.log("Treating as single URL");
        return [task.result_urls];
      }

      return [];
    }
  }

  /**
   * 检查任务是否已完成（成功或失败）
   */
  static isCompleted(task: PetPortraitTask): boolean {
    return task.status === TaskStatus.Completed || task.status === TaskStatus.Failed;
  }

  /**
   * 检查任务是否超时（创建超过指定时间仍未完成）
   */
  static isTimeout(task: PetPortraitTask, timeoutMinutes = 10): boolean {
    if (this.isCompleted(task)) return false;
    
    const now = new Date();
    const createdAt = new Date(task.created_at);
    const diffMinutes = (now.getTime() - createdAt.getTime()) / (1000 * 60);
    
    return diffMinutes > timeoutMinutes;
  }
}
